import logging
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta

from odoo import SUPERUSER_ID, api, fields, models, _
from odoo.exceptions import ValidationError

class AccountJustification(models.Model):
    _inherit = "account.justification"

    purchase_request_ids = fields.One2many('purchase.request', 'justification_id', string='Purchase Request')
    remaining_amount_pr = fields.Monetary(compute='_compute_remaining_amount_pr', string='Remaining Amount PR', currency_field='company_currency_id', store=True)
    all_line_fpjp = fields.Boolean(
        string="All Lines FPJP",
        compute="_compute_all_line_fpjp",
        store=True
    )

    @api.depends('line_ids.procurement_type_id.name', 'line_ids.procurement_type_id')
    def _compute_all_line_fpjp(self):
        for record in self:
            if record.line_ids:
                record.all_line_fpjp = all(
                    line.procurement_type_id.name == 'FPJP' for line in record.line_ids
                )
            else:
                record.all_line_fpjp = False

    @api.depends('purchase_request_ids.estimated_cost', 'purchase_request_ids.state', 'budget_reserved', 'purchase_request_ids.line_ids')
    def _compute_remaining_amount_pr(self):
        for rec in self:
            total_pr_amount = sum(pr.estimated_cost for pr in rec.purchase_request_ids if pr.state not in ['rejected', 'return', 'canceled'])
            total_pr_reserved = sum(rec.line_ids.filtered(lambda x: x.procurement_type_id.name != 'FPJP').mapped('amount_currency'))
            rec.remaining_amount_pr = total_pr_reserved - total_pr_amount

            purchase_request = self.env['purchase.request'].search([('justification_id.budgetary_position_id', '=', rec.budgetary_position_id.id),
                                                    ('state', 'not in', ['rejected', 'return', 'canceled'])])

            rec.budgetary_position_id.pr_reserve_amount = sum(purchase_request.mapped('estimated_cost'))


class AccountJustificationLine(models.Model):
    _inherit = "account.justification.line"
    _rec_name = "description"

    
    purchase_request_line_ids = fields.One2many('purchase.request.line', 'justification_line_id', string='Purchase Request Line')