<odoo>

    <record id="inherit_purchase_request_form" model="ir.ui.view">
        <field name="name">inherit.purchase.request.form</field>
        <field name="model">purchase.request</field>
        <field name="inherit_id" ref="purchase_request.view_purchase_request_form"/>
        <field name="arch" type="xml">

            <xpath expr="//field[@name='requested_by']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='assigned_to']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='date_start']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='picking_type_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='currency_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//group[@class='oe_subtotal_footer oe_right']//field[@name='currency_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//group[@class='oe_subtotal_footer oe_right']//field[@name='estimated_cost']" position="attributes">
                <attribute name="string">Total</attribute>
            </xpath>

            <xpath expr="//field[@name='origin']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='description']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='po_numbers']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='group_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='name']" position="attributes">
                <attribute name="readonly">1</attribute>
            </xpath>

            <xpath expr="//button[@name='button_to_approve']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//button[@name='button_draft']" position="attributes">
                <attribute name="invisible">state not in ('approved','rejected','done','canceled')</attribute>
            </xpath>

            <xpath expr="//field[@name='state']" position="attributes">
                <attribute name="statusbar_visible">draft,to_approve,assign_buyer,approved,rejected,canceled</attribute>
            </xpath>

            <xpath expr="//button[@name='button_approved']" position="attributes">
                <attribute name="invisible">state != 'assign_buyer'</attribute>
            </xpath>

            <xpath expr="//button[@name='button_rejected']" position="attributes">
                <attribute name="invisible">state == 'approved' or state == 'draft' or state == 'assign_buyer' or state == 'rejected' or state == 'canceled' or state == 'done'</attribute>
            </xpath>
<!-- 
            <xpath expr="//button[@name='586']" position="attributes">
                <attribute name="invisible">state == 'approved</attribute>
            </xpath>

            <xpath expr="//button[@name='591']" position="attributes">
                <attribute name="invisible">state == 'approved</attribute>
            </xpath> -->

            <xpath expr="//button[@name='586']" position="replace">
                <button name="586" invisible="1" string="Create RFQ" type="action"/>
            </xpath>


            <xpath expr="//button[@name='button_rejected']" position="after">
                <button name="action_submit" string="Submit" type="object" class="oe_highlight" invisible="state != 'draft'"/>
                <button name="action_asign_buyer" string="Asign Buyer" type="object" class="oe_highlight" invisible="state != 'to_approve'"/>
                <button name="action_cancel_wizard" string="Cancel" type="object" invisible="state not in ('draft', 'approved', 'to_approve', 'assign_buyer')"/>
                <button name="action_withdraw_approval" string="Set To Draft" type="object" invisible="state == 'draft' or state == 'done'"/>
                <button name="button_reassign_pic" string="Re-assign PIC" type="object" invisible="state not in ('assign_buyer', 'approved')"/>
                <button name="button_reassign_buyer" string="Re-assign Buyer" type="object" invisible="state != 'approved'"/>
            </xpath>

            <xpath expr="//group[1]" position="after">
                <group>
                    <group>
                        <field name="pr_type" required="1" readonly="state != 'draft'"/>
                        <field name="justification_id" required="1" readonly="state != 'draft'" domain="[('state', '=', 'approved'), ('line_ids', '!=', False),('all_line_fpjp','!=',True)]" context="{'list_view_ref': 'ap_purchase_request.account_justification_pr_view_tree'}"/>
                        <!-- <field name="agreement_id" domain="[('state', '=', 'submit'),('is_purchase_request','!=',True),('purchase_request_exist', '=', False), ('active', '=', True)]" invisible="pr_type != 'rfp'" required="pr_type == 'rfp'" readonly="state != 'draft'"/> -->
                        <field name="agreement_id" domain="[('state', '=', 'submit'), ('active', '=', True)]" invisible="pr_type != 'rfp'" required="pr_type == 'rfp'" readonly="state != 'draft'" context="{'show_agreement_name': True, 'search_view_ref': 'ap_bidding_agreement.ap_agreement_view_search'}"/>
                        <field name="rfq_id" domain="[('state', '=', 'submit'),('is_purchase_request','!=',True),('purchase_request_exist', '=', False), ('active', '=', True)]" invisible="pr_type != 'rfq'" required="pr_type == 'rfq'" readonly="state != 'draft'"/>
                        <field name="description" readonly="state != 'draft'"/>
                        <field name="employee_id" readonly="state != 'draft'" invisible="1"/>
                        <field name="pr_category_id" required="is_dpl == False" readonly="state != 'draft'"/>
                        <field name="pic_id" required="1" readonly="1"/>
                        <!-- <field name="requested_by" required="1" readonly="state != 'draft'"/> -->
                        <field name="requestor_id" required="1" readonly="state != 'draft'"/>
                        <field name="unit_id" required="0" readonly="1" force_save="1"/>
                        <field name="hr_group_id" required="0" readonly="1" force_save="1"/>
                        <field name="directorate_id" required="0" readonly="1" force_save="1"/>
                        <field name="buyer_id" required="state in ('assign_buyer')" readonly="state not in ('draft','to_approve','assign_buyer')" invisible="state in ('draft','to_approve')"/>
                        <field name="assigne_buyer_date" required="state in ('assign_buyer')" readonly="state not in ('draft','to_approve','assign_buyer')" invisible="state in ('draft','to_approve')"/>
                    </group>
                    <group>
                        <field name="date_start" required="0" readonly="1"/>
                        <field name="date_end" required="0" readonly="1"/>
                        <field name="is_dpl" invisible="not is_dpl" readonly="state != 'draft'" force_save="1" />
                        <field name="dpl_id" readonly="state != 'draft'" invisible="not is_dpl" required="is_dpl" domain="[('justification_id', '=', justification_id), ('state','=','done')]" />
                        <field name="rkap_id" required="1" readonly="1" force_save="1"/>
                        <field name="rkap_code" required="0" readonly="1" force_save="1"/>
                        <field name="rkap_category_id" required="0" readonly="1" force_save="1"/>
                        <field name="rkap_type_id" required="0" readonly="1" force_save="1"/>
                        <field name="accrue_expense" readonly="state != 'to_approve'" invisible="1"/>
                        <field name="is_project_readonly" invisible="1"/>
                        <field name="project_id" required="is_project_readonly" invisible="not is_project_readonly" readonly="state != 'draft'" />
                        <field name="attachment_ids" widget="many2many_binary" readonly="state != 'draft'" required="1"/>
                        <field name="total_manajemen_fee" invisible="1"/>
                        <field name="product_agreement_ids" invisible="1"/>
                        <field name="product_tmpl_agreement_ids" invisible="1"/>
                    </group>
                </group>
            </xpath>

            <xpath expr="//field[@name='line_ids']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='line_ids']" position="after">
                <field name="line_ids" readonly="state != 'draft'">
                    <list decoration-muted="cancelled == True" editable="bottom">
                        <field name="justification_line_id" width="100px" required="1"/>
                        <field name="pr_type"/>
                        <field name="product_tmpl_agreement_id" column_invisible="parent.agreement_id == False" string="Product" domain="[('id', 'in', parent.product_tmpl_agreement_ids)]"/>
                        <field name="product_agreement_id" column_invisible="parent.agreement_id == False" string="Product Variant" domain="[('id', 'in', parent.product_agreement_ids), ('product_tmpl_id', '=', product_tmpl_agreement_id)]"/>
                        <field name="product_tmpl_id" column_invisible="parent.agreement_id != False"/>
                        <field name="product_id" column_invisible="parent.agreement_id != False" string="Product Variant" domain="[('product_tmpl_id', '=', product_tmpl_id), ('is_product_variant', '=', True)]" width="150px"/>
                        <field name="product_category_id" readonly="1" force_save="1" width="150px"/>
                        <field name="name"/>
                        <field name="domain_currency" column_invisible="1" />
                        <field name="new_currency_id" domain="domain_currency" width="100px" required="1" readonly="product_agreement_id != False"/>
                        <field name="currency_rate" string="Rate" widget="float" width="50px"/>
                        <field name="product_qty" required="1"/>
                        <field name="product_uom_category_id" column_invisible="1"/>
                        <field name="product_uom_id" required="1" readonly="product_agreement_id != False"/>
                        <field name="is_percentage" force_save="1" width="100px"/>
                        <field name="analytic_distribution" widget="analytic_distribution" groups="analytic.group_analytic_accounting" options="{'product_field': 'product_id', 'business_domain': 'purchase_order'}" column_invisible="1"/>
                        <field name="date_required" column_invisible="1"/>
                        <field name="price_unit" widget="monetary" required="0" readonly="(parent.pr_type in ('rfq', 'rfp') and unit_price_source != 0) or (product_agreement_id != False and price_unit > 0)" force_save="1"/>
                        <field name="subtotal_currency" widget="monetary" readonly="1" force_save="1" width="130px"/>
                        <field name="estimated_cost" widget="monetary" string="Subtotal IDR" readonly="1" force_save="1"/>
                        <field name="justif_line_fund_available" widget="monetary" width="170px" readonly="1" force_save="1" column_invisible="1"/>
                        <field name="start_date" width="130px" required="1"/>
                        <field name="end_date" width="130px" required="1"/>
                        <field name="is_manajemen_fee" invisible="is_percentage == True" width="130px"/>
                        <field name="currency_id" column_invisible="1"/>
                        <field name="company_id" groups="base.group_multi_company" widget="selection" column_invisible="1"/>
                        <field name="cancelled" column_invisible="1"/>
                        <field name="is_editable" column_invisible="1"/>
                        <field name="purchased_qty" string="PO Qty" force_save="1"/>
                        <field name="remaining_amount" column_invisible="1" force_save="1"/>
                        <field name="purchase_state" force_save="1"
                            widget="badge"
                            decoration-success="purchase_state == 'done'"
                            attrs="{'invisible': [('purchase_state', '=', False)]}" />
                        <!-- <field name="purchase_state" widget="badge" decoration-success="purchase_state == ('done')" decoration-muted="purchase_state == ('draft')" decoration-info="purchase_state in ('sent', 'purchase')" decoration-warning="purchase_state == ('to_approve')" decoration-danger="purchase_state == 'cancelled'" width="130px"/> -->
                        <button name="action_show_details" type="object" icon="fa-list" title="Show Details" width="0.1" options="{&quot;warn&quot;: true}"/>
                    </list>
                </field>
            </xpath>

        </field>
    </record>
    <record id="purchase_request_inherit_form_view" model="ir.ui.view">
        <field name="name">purchase.request.inherit.form.view</field>
        <field name="model">purchase.request</field>
        <field name="inherit_id" ref="purchase_request.view_purchase_request_form"/>
        <field name="arch" type="xml">

            <xpath expr="//header/button[@name='%(account_budget_control_ent.action_purchase_request_line_make_purchase_agreement)d']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//button[@name='button_draft']" position="replace">
                <button
                        name="button_draft"
                        string="Reset"
                        invisible="1"
                        type="object"
                        groups="purchase_request.group_purchase_request_manager"
                    />
            </xpath>
        </field>
    </record>
    <record id="purchase_request_line_form_inherit" model="ir.ui.view">
        <field name="name">purchase.request.line.form.inherit</field>
        <field name="model">purchase.request.line</field>
        <field name="inherit_id" ref="purchase_request.view_purchase_request_line_details"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='product_id']" position="before">
                <field name="product_tmpl_id" readonly="is_editable == False"/>
            </xpath>
            <xpath expr="//field[@name='product_id']" position="attributes">
                <attribute name="string">Product Variant</attribute>
            </xpath>
        </field>
    </record>

</odoo>