<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_weha_account_move_form" model="ir.ui.view">
            <field name="name">view account.move inherited</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form" />
            <field name="arch" type="xml">
                <xpath expr="//notebook//list//field[@name='price_subtotal']" position="attributes">
                    <attribute name="string">Subtotal</attribute>
                </xpath>
                <xpath expr="//notebook//list//field[@name='price_total']" position="attributes">
                    <attribute name="string">Total</attribute>
                </xpath>
                <xpath expr="//page[@name='account_document']" position="attributes">
                    <attribute name="invisible">move_type not in ['out_refund', 'in_invoice', 'in_refund']</attribute>
                </xpath>
                <xpath expr="//field[@name='invoice_line_ids']" position="after">
                    <group class="oe_subtotal_footer oe_right" invisible="move_type != 'out_invoice'">
                        <field name="amount_untaxed" string="DPP" widget="monetary"/>
                        <field name="dpp_nilai_lain" string="DPP Nilai Lain" widget="monetary"/>
                        <field name="amount_tax" string="Taxes" widget="monetary"/>
                        <field name="amount_total" string="Amount Total" widget="monetary"/>
                        <field name="amount_residual" string="Amount Due" widget="monetary"/>
                    </group>
                </xpath>
            </field>
        </record>

        <record id="view_move_form_ext" model="ir.ui.view">
            <field name="name">Weha view account.move inherited ext</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="vendor_bill_linkaja.view_move_form_ext" />
            <field name="arch" type="xml">
                <xpath expr="//button[@name='action_reject']" position="attributes">
                    <attribute name="invisible">move_type != 'in_invoice' or validation_state not in ['approve']</attribute>
                </xpath>
                <xpath expr="//button[@name='button_cancel']" position="attributes">
                    <attribute name="invisible">move_type in ['entry'] or validation_state in ['cancel','rejected']</attribute>
                </xpath>
            </field>
        </record>
        
    </data> 
</odoo>
