<odoo>

    <record id="procurement_view_form" model="ir.ui.view">
        <field name="name">procurement.view</field>
        <field name="model">procurement.update.data</field>
        <field name="arch" type="xml">
            <form string="Update Informasi">
                
                <header>
                    <button name="action_save" string="Save" type="object" class="btn-primary" invisible="1"/>
                    <button name="action_discard" string="Discard" type="object" class="btn-secondary" invisible="1"/>
                    <button name="action_pending_approval" string="Submit" type="object" class="btn-warning" 
                            invisible="procurement_state != 'draft'"/> 
                    <button name="action_approve" string="Approve" type="object" class="btn-success" 
                            invisible="procurement_state != 'pending_approval'"/>    
                    <field name="procurement_state" force_save="1" widget="statusbar"/>
                </header>

                <sheet>
                    <group colspan="4">
                        <div class="col-12">
                            <div class="o_row">
                                <div class="col-3">
                                    <field name="general_info_checked" 
                                        readonly="bank_account_checked == True or tax_status_checked == True or term_payment_checked == True"/>
                                    <label for="general_info_checked"/>
                                </div>
                                <div class="col-3">
                                    <field name="bank_account_checked" 
                                        readonly="general_info_checked == True or tax_status_checked == True or term_payment_checked == True"/>
                                    <label for="bank_account_checked"/>
                                </div>
                                <div class="col-3">
                                    <field name="tax_status_checked" 
                                        readonly="general_info_checked == True or bank_account_checked == True or term_payment_checked == True"/>
                                    <label for="tax_status_checked"/>
                                </div>
                                <div class="col-3">
                                    <field name="term_payment_checked" 
                                        readonly="general_info_checked == True or bank_account_checked == True or tax_status_checked == True"/>
                                    <label for="term_payment_checked"/>
                                </div>
                            </div>
                        </div>
                    </group>

                    <group>
                        <group>
                            <field name="reference_number" nolabel="1" readonly="1"/>
                            <field name="partner_id" invisible="1"/>
                            <field name="company_name"/>
                        </group>
                        <group>
                            <span class="o_form_label o_td_label o_address_type" name="company_address">
                                <span>Address</span>
                            </span>
                            <div class="o_address_format">
                                <field name="company_address" placeholder="Street..." class="o_address_street"/>
                                <field name="street2" placeholder="Street 2..." class="o_address_street"/>
                                <field name="city" placeholder="City" class="o_address_city"/>
                                <field name="state_id" class="o_address_state" placeholder="State" options="{'no_open': True, 'no_quick_create': True}" context="{'country_id': country_id, 'default_country_id': country_id, 'zip': zip}"/>
                                <field name="zip" placeholder="ZIP" class="o_address_zip"/>
                                <div name="partner_address_country" class="d-flex justify-content-between">
                                    <field name="country_id" placeholder="Country" class="o_address_country" options="{&quot;no_open&quot;: True, &quot;no_create&quot;: True}"/>
                                </div>
                            </div>
                        </group>
                    </group>

                    <group invisible="not tax_status_checked">
                        <field name="tax_document"/>
                        <field name="tax_organization_type"/>
                        <field name="npwp_number" invisible="tax_document != 'npwp'"/>
                        <field name="ktp_number" invisible="tax_document != 'ktp'"/>
                        <field name="cor_number" invisible="tax_document != 'others'"/>
                    </group>

                    <group invisible="not term_payment_checked">
                        <field name="payment_term_id"/>
                    </group>


                    <notebook>
                        <!-- <page string="Contact" invisible="not general_info_checked">
                            <field name="contact_ids" mode="kanban" editable="bottom">
                                <kanban>
                                    <templates>
                                        <t t-name="kanban-box">
                                            <div class="oe_kanban_global_click o_kanban_card">
                                                <div class="o_kanban_details">
                                                    <strong><field name="contact_name"/></strong>
                                                    <div><field name="contact_email" widget="email"/></div>
                                                    <div><field name="contact_number" widget="phone"/></div>
                                                </div>
                                            </div>
                                        </t>
                                    </templates>
                                </kanban>
                                <form string="Contact">
                                    <sheet>
                                        <group>
                                            <field name="contact_name" />
                                            <field name="contact_email"/>
                                            <field name="contact_number"/>
                                        </group>
                                    </sheet>
                                </form>
                            </field>
                        </page> -->

                        <page string="Contacts &amp; Addresses" name="contact_addresses" autofocus="autofocus" invisible="not general_info_checked">
                            <field name="contact_ids" mode="kanban" context="{'default_parent_id': id}">
                                <kanban color="color">
                                    <field name="color"/>
                                    <field name="type"/>
                                    <field name="is_company"/>
                                    <templates>
                                        <t t-name="card" class="flex-row">
                                            <aside class="o_kanban_aside_full">
                                                <field name="avatar_128" class="o_kanban_image_fill w-100" widget="image" options="{'img_class': 'object-fit-cover'}" alt="Contact image"/>
                                            </aside>
                                            <main class="ps-2 ps-md-0">
                                                <field name="name" class="fw-bold"/>
                                                <field name="function"/>
                                                <field name="email" widget="email"/>
                                                <div t-if="record.type.raw_value != 'contact'">
                                                    <div>
                                                        <field class="me-1" name="zip"/>
                                                        <field name="city"/>
                                                    </div>
                                                    <field class="me-1" name="state_id"/>
                                                    <field name="country_id"/>
                                                </div>
                                                <div t-if="record.phone.raw_value">Phone: <field name="phone"/></div>
                                                <div t-if="record.mobile.raw_value">Mobile: <field name="mobile"/></div>
                                            </main>
                                        </t>
                                    </templates>
                                </kanban>
    
                                <form string="Contact / Address">
                                    <sheet>
                                        <field name="type" required="1" widget="radio" options="{'horizontal': true}"/>
                                        <div class="text-muted oe_edit_only">
                                            <p class="mb-0" invisible="type != 'contact'"><span>Use this to organize the contact details of employees of a given company (e.g. CEO, CFO, ...).</span></p>
                                            <p class="mb-0" invisible="type != 'invoice'"><span>Preferred address for all invoices. Selected by default when you invoice an order that belongs to this company.</span></p>
                                            <p class="mb-0" invisible="type != 'delivery'"><span>Preferred address for all deliveries. Selected by default when you deliver an order that belongs to this company.</span></p>
                                            <p class="mb-0" invisible="type != 'followup'"><span>Preferred address for follow-up reports. Selected by default when you send reminders about overdue invoices.</span></p>
                                            <p class="mb-0" invisible="type != 'other'"><span>Other address for the company (e.g. subsidiary, ...)</span></p>
                                        </div>
                                        <hr/>
                                        <group>
                                            <group>
                                                <field name="name" string="Contact Name" required="type == 'contact'"
                                                     placeholder="e.g. New Address"/>
                                                <field name="title" options="{'no_open': True}" placeholder="e.g. Mr."
                                                    invisible="type != 'contact'"/>
                                                <field name="function" placeholder="e.g. Sales Director"
                                                    invisible="type != 'contact'"/>
                                                <field name="is_bidding" />
                                                <field name="partner_pic_id" invisible="1" force_save="1" />
                                                <label for="street" string="Address" invisible="type == 'contact'"/>    
                                                <div invisible="type == 'contact'">
                                                    <div class="o_address_format" name="div_address">
                                                        <field name="street" placeholder="Street..." class="o_address_street"/>
                                                        <field name="street2" placeholder="Street 2..." class="o_address_street"/>
                                                        <field name="city" placeholder="City" class="o_address_city"/>
                                                        <field name="state_id" class="o_address_state" placeholder="State" options="{'no_open': True, 'no_quick_create': True}" context="{'country_id': country_id, 'default_country_id': country_id, 'zip': zip}"/>
                                                        <field name="zip" placeholder="ZIP" class="o_address_zip"/>
                                                        <field name="country_id" placeholder="Country" class="o_address_country" options='{"no_open": True, "no_create": True}'/>
                                                    </div>
                                                </div>
                                            </group>
                                            <group>
                                                <field name="email" widget="email"/>
                                                <field name="phone" widget="phone"/>
                                                <field name="mobile" widget="phone"/>
                                                <!-- <field name="company_id" invisible="1"/>  -->
                                            </group>
                                        </group>
                                        <group>
                                            <field name="comment" placeholder="Internal notes..." nolabel="1"/>
                                        </group>
                                        <field name="lang" invisible="1"/>
                                    </sheet>
                                </form>
                            </field>
                        </page>

                        <page string="Accounting" invisible="not bank_account_checked">
                            <group string="Bank Accounts" name="banks" >
                                <field name="partner_id" invisible="1"/>
                                <field name="bank_procurement_ids" nolabel="1"  widget="one2many_list">
                                    <list>
                                        <!-- <field name="acc_number"/> -->
                                        <field name="bank_id"/>
                                        <field name="account_number" />
                                        <!-- <field name="allow_out_payment" string="Send Money"/> -->
                                        <!-- <field name="acc_holder_name" column_invisible="True"/> -->
                                        <field name="tipe_rekening_bank"/>
                                    </list>
                                    <form string="Bank Account">
                                        <sheet>
                                            <group>
                                                <group>
                                                    <field name="acc_number"/>
                                                    <field name="bank_id"/>
                                                    <field name="account_holder"/>
                                                    <field name="is_sub_account"/>
                                                    <field name="main_partner_bank_id" required="is_sub_account == True" invisible="is_sub_account != True" />
                                                    <field name="main_account_number" invisible="is_sub_account != True" />
                                                    <field name="branch_name"/>
                                                    <field name="branch_code"/>
                                                    <field name="swift_code"/>
                                                    <field name="bank_bic" string="SWIFT Code" invisible="1"/>
                                                    <field name="account_number" required="1"/>
                                                    <field name="tipe_rekening_bank"/>
                                         
                                                </group>
                                                <group >
                                                    <field name="currency_id"/>
                                                    <field name="allow_out_payment" string="Send Money"/>
                                                </group>
                                            </group>                         
                                        </sheet>
                                    </form>
                                </field>
                            </group>
                        </page>

                        <page string="Approval" invisible="1" >
                            <field name="approval_ids">
                                <list editable="bottom" order="sequence">
                                    <field name="sequence"/>
                                    <field name="approval_name"/>
                                    <field name="approval_position"/>
                                    <field name="approval_status"/>
                                    <field name="approval_date"/>
                                    <field name="approval_note"/>
                                    <field name="reassign_to"/>
                                    <field name="reassign_status"/>
                                    <field name="reassign_date"/>
                                    <field name="reassign_note"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="procurement_view_tree" model="ir.ui.view">
        <field name="name">procurement.update.data.tree</field>
        <field name="model">procurement.update.data</field>
        <field name="arch" type="xml">
            <list string="Procurement Updates">
                <!-- <field name="name" string="Update Name" invisible="1"/>
                <field name="partner_id" string="Vendor" invisible="1"/> -->
                <!-- <field name="sequence" string="No."/> -->
                <field name="reference_number" string="No."/>
                <field name="company_name" string="Company"/>
                <field name="procurement_state" string="Status"/>
                <field name="create_date" string="Created On"/>
                <field name="create_uid" string="Created By"/>
            </list>
        </field>
    </record>

    <record id="view_procurement_update_filter" model="ir.ui.view">
        <field name="name">procurement.update.data.filter</field>
        <field name="model">procurement.update.data</field>
        <field name="arch" type="xml">
            <search string="Search Procurements">
                <field name="reference_number"/>
                <field name="partner_id"/>
                <field name="create_uid"/>
                <field name="procurement_state"/>
                <separator/>
                <group expand="0" name="group_by" string="Group By">
                    <filter name="group_procurement_state" string="Status" context="{'group_by': 'procurement_state'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="action_procurement_update_data" model="ir.actions.act_window">
        <field name="name">Procurement Updates</field>
        <field name="res_model">procurement.update.data</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">No procurement updates found</p>
        </field>
    </record>

</odoo>