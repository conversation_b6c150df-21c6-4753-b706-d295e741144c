from odoo import fields, models, api
from datetime import datetime
from odoo.exceptions import UserError


class PartnerVP(models.TransientModel):
    _name = 'partner.vp'
    _description = 'Partner VP'


    partner_id = fields.Many2one('res.partner', 'Partner')
    name = fields.Char('Company')
    contact_name = fields.Char('Contact Name')
    contact_email = fields.Char('Contact Email')
    tax_organization_type = fields.Many2one('tax.organization.type', string="Tax Organization Type")
    busines_category = fields.Many2one('supplier.business.category', string="Kategori <PERSON>")


    def _get_email_to_vendor(self):
        """Return external ID of the mail template used to notify the vendor."""
        return 'partner_linkaja.email_to_vendor'

    def send_email_to_vendor(self, email_to, template_xml_id):
        """Send an email to the vendor using a predefined template."""
        mail_from = self.env.company.email_formatted

        try:
            template = self.env.ref(template_xml_id)
        except ValueError:
            raise UserError(_('Template email "%s" tidak ditemukan.') % template_xml_id)

        if not email_to:
            raise UserError(_('Alamat email tujuan tidak ditemukan.'))

        if template:
            template.with_context(
                mail_from=mail_from,
                mail_to=email_to,
                company=self.env.company.name,
                email_sender=self.env.user.email_formatted,
                name_sender=self.env.user.employee_id.name or self.env.user.name
            ).send_mail(self.id, force_send=True)

    def action_generate(self):
        """Update partner, create contact, and send email to vendor."""
        self.ensure_one()

        partner = self.partner_id

        # Update data partner
        partner.write({
            'name': self.name,
            'tax_organization_type': self.tax_organization_type.id,
            'busines_category': self.busines_category.id,
        })

        # Kirim email
        # self.send_email_to_vendor(self.contact_email, self._get_email_to_vendor())

        return {'type': 'ir.actions.act_window_close'}

