from odoo import fields, models, api, _
from odoo.tools.misc import unique
from odoo.exceptions import UserError



class ResPartner(models.Model):
    _inherit = 'res.partner'

    def send_email_to_vp_from_contact(self):
        self.action_vp_email()

    def _get_email_to_vp(self):
        return 'ap_partner_mail.email_to_vendor3'

    def send_email_to_vp(self, email_to, activation_link, template_xml_id):
        """Send an email to the vendor using a predefined template."""
        mail_from = self.env.company.email_formatted or self.env.user.email_formatted

        try:
            template = self.env.ref(template_xml_id)
        except ValueError:
            raise UserError(_('Template email "%s" tidak ditemukan.') % template_xml_id)

        if not email_to:
            raise UserError(_('Alamat email tujuan tidak ditemukan.'))

        if template:
            template.with_context(
                mail_from=mail_from,
                mail_to=email_to,
                activation_link=activation_link,
                company=self.env.company.name,
                email_sender=self.env.user.email_formatted,
                name_sender=self.env.user.employee_id.name or self.env.user.name
            ).send_mail(self.id, force_send=True)

    def action_vp_email(self):
        self.ensure_one()
        if not self.email:
            raise UserError(_('Silakan isi Email terlebih dahulu.'))

        email_to = "%s" % (self.email)

        # activation_link = "{}create-password?email={}&partner_id={}".format(
        #     self.env.company.vp_website if self.env.company.vp_website.endswith('/') else self.env.company.vp_website + '/',
        #     self.email,
        #     self.id
        # )

        activation_link = "{}create-password?email={}&partner_pic_id={}&partner_id={}".format(
            self.env.company.vp_website if self.env.company.vp_website.endswith('/') else self.env.company.vp_website + '/',
            self.email,
            self.id,
            self.parent_id.id
        )
        self.send_email_to_vp(email_to, activation_link, self._get_email_to_vp())

        # return {
        #     'type': 'ir.actions.client',
        #     'tag': 'display_notification',
        #     'params': {
        #         'title': _('Email Terkirim'),
        #         'message': _('Email berhasil dikirim ke %s.') % self.email,
        #         'type': 'success',
        #         'sticky': False,
        #     }
        # }

    def action_submit(self):
        res = super(ResPartner, self).action_submit()
        self.ensure_one()
        # self.action_vp_email()
        return res
