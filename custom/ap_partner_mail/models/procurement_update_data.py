from odoo import models, fields, api
from datetime import datetime


class ProcurmentUpdateData(models.Model):
    _inherit = "procurement.update.data"


    def action_approve(self):
        super(ProcurmentUpdateData, self).action_approve()
        for record in self:
            # send email invitation vp
            if record.general_info_checked:
                partners = record.partner_id.child_ids.filtered(
                    lambda x: x.is_bidding
                )

                for partner in partners:
                    vp_account = self.env['vp.account.master'].search(
                        [('parent_id', '=', partner.id)],
                        limit=1
                    )
                    
                    if not vp_account:
                        partner.send_email_to_vp_from_contact()
