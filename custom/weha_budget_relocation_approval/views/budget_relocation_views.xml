<odoo>
    <data>
        <record id="budget_relocation_loa_form" model="ir.ui.view">
            <field name="name">account.relocation.loa.form</field>
            <field name="model">account.relocation</field>
            <field name="inherit_id" ref="account_budget_relocation.account_relocation_view_form"/>
            <field name="arch" type="xml">
                <!-- add button to do action -->
                <xpath expr="//header" position="inside">
                    <button name="action_budget_submit" string="Submit" type="object" class="oe_highlight" invisible="state != 'draft'"/>
                    
                    <button name="button_action" type="object" string="Approve" class="btn btn-success" 
                        invisible="state != 'pending_approval' or not is_current_approver"
                        context="{'approve': True}" />
                    <button name="button_action" type="object" string="Reject" class="btn-danger"
                        invisible="state != 'pending_approval' or not is_current_approver"
                        context="{'reject': True}" />
                    <button name="button_action" type="object" string="Reassign" class="btn btn-success" 
                        invisible="state != 'pending_approval' or (not is_current_approver and not is_admin)"
                        context="{'reassign': True}" />
                    <button name="action_return" type="object" string="Return" class="btn btn-success"
                        invisible="state != 'pending_approval' or not is_current_approver" />
                    <button name="action_resubmit" type="object" string="Submit" class="btn btn-success" 
                        invisible="state != 'return'" />
                </xpath>

                <xpath expr="//field[@name='period_id']" position="before">
                    <field name="hierarchy_id" readonly="1" force_save='1'/>
                </xpath>

                <xpath expr="//field[@name='requestor_id']" position="attributes">
					<attribute name="required">True</attribute>
				</xpath>

                <!-- add approval_history_ids in new page inside notebook -->
                <xpath expr="//notebook" position="inside">
                    <page name="history" string="Approvals">
                        <field name="is_current_approver" invisible="1"/>
                        <field name="selected_approver_ids" widget="many2many_tags" invisible="1"/>
                        <field name="approval_history_ids" readonly="1" force_save='1' />
                    </page>
                    <page string="Approval Details" name="history_detail">
                        <field name="approval_history_detail_ids" >
                            <list editable="bottom" create="0" edit="0" delete="0">
                                <field name="level" readonly="1" force_save='1' />
                                <field name="employee_id" readonly="1" force_save='1' />
                                <field name="employee_state" readonly="1" force_save='1' />
                                <field name="employee_date" readonly="1" force_save='1' />
                                <field name="employee_note" readonly="1" force_save='1' />
                                <field name="reassign_employee_id" readonly="1" force_save='1' />
                                <field name="reassign_employee_state" readonly="1" force_save='1' />
                                <field name="reassign_employee_date" readonly="1" force_save='1' />
                                <field name="reassign_employee_note" readonly="1" force_save='1' />
                            </list>
                        </field>
                    </page>
                    <page name="approval_message" string="Message Details">
                        <field name="approval_message_ids" readonly="1" force_save='1' />
                    </page>
                </xpath>

                <xpath expr="//button[@name='action_budget_approve']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>

                <!-- <xpath expr="//button[@name='action_reject']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath> -->

                <!-- <xpath expr="//page[@name='approvers']" position="attributes">
                    <attribute name="invisible">True</attribute>
                </xpath> -->
            </field>
        </record>

        <!-- <record id="budget_relocation_view_search" model="ir.ui.view">
            <field name="name">budget.relocation.search</field>
            <field name="model">bank.cash.transfer</field>
            <field name="priority">1</field>
            <field name="arch" type="xml">
                <search>
                    <separator/>
                    <filter name="my_approvals" string="My Approvals" domain="[('selected_approver_ids.user_id', '=', uid)]" />
                    <separator/>
                </search>
            </field>
        </record> -->

        <record id="account_budget_relocation.action_account_relocation" model="ir.actions.act_window">
            <field name="context">{'search_default_my_approvals': 1}</field>
            <field name="path">budget-relocation</field>
        </record>

        <record id="view_budget_relocation_filter" model="ir.ui.view">
            <field name="name">view.account.relocation.filter</field>
            <field name="model">account.relocation</field>
            <field name="arch" type="xml">
                <search>
                    <separator/>
                    <filter name="my_approvals" string="My Approvals" domain="[('selected_approver_ids.user_id', '=', uid)]" />
                    <separator/>
                </search>
            </field>
        </record>
    </data>
</odoo>
