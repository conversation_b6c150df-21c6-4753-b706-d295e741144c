# -*- coding: utf-8 -*-

from io import BytesIO

from odoo import _, api, models, fields
from odoo.tools import DEFAULT_SERVER_DATETIME_FORMAT
from odoo.tools.misc import xlsxwriter


class PaymentVerificationWizard(models.TransientModel):
    _name = 'payment.verification.wizard'
    _description = 'Payment Input Verification Report'

    date_from = fields.Date(string='From Date', required=True)
    date_to = fields.Date(string='To Date', required=True)

    @api.onchange('date_from', 'date_to')
    def onchange_periode(self):
        if self.date_from and self.date_to and self.date_to < self.date_from:
            self.date_from = False
            self.date_to = False
            return {
                'warning': {
                    'title': 'Warning',
                    'message': 'Cannot back date!',
                },
            }

    def action_export_xlsx(self):
        self.ensure_one()
        self_tz = self.with_context(tz=self.env.user.tz or 'Asia/Jakarta')
        date = fields.Datetime.context_timestamp(self_tz, fields.Datetime.now())
        name = f'{self._description} - {date.strftime(DEFAULT_SERVER_DATETIME_FORMAT)}'
        return {
            'type': 'ir.actions.act_url',
            'url': '/xlsx_report/%s/%s/%s' % (self._name, self.id, name),
            'target': 'new',
        }

    def _generate_xlsx(self, response):
        """Function to generate xlsx report"""
        payments = self.env['account.payment'].search(
            [
                ('date', '>=', self.date_from),
                ('date', '<=', self.date_to),
                ('state', '!=', 'draft'),
                ('payment_invoice_ids', '!=', False),
            ]
        )

        output = BytesIO()
        workbook = xlsxwriter.Workbook(
            output, {'in_memory': True, 'strings_to_formulas': False}
        )
        worksheet = workbook.add_worksheet(_('Accounts coverage'))

        s_title = workbook.add_format(
            {'bold': 1, 'align': 'center', 'font_size': 10, 'font_name': 'Arial'}
        )
        s_title_date = workbook.add_format(
            {'align': 'right', 'num_format': 'dd/mm/yyyy', 'font_size': 10}
        )
        s_header = workbook.add_format(
            {
                'bold': 1,
                'font_name': 'Arial',
                'font_size': 8,
                'valign': 'vcenter',
                'num_format': '#,###',
                'border': 1,
                'bg_color': '#bdd8ed',
                'text_wrap': True,
            }
        )
        s_normal = workbook.add_format(
            {
                'font_name': 'Arial',
                'font_size': 8,
                'num_format': '#,###',
                'border': 1,
                'text_wrap': True,
            }
        )
        s_date = workbook.add_format(
            {'align': 'right', 'num_format': 'dd/mm/yyyy', 'font_size': 10, 'border': 1}
        )
        s_num = workbook.add_format(
            {
                'align': 'right',
                'font_size': 8,
                'font_name': 'Arial',
                'border': 1,
                'num_format': '#,##0',
            }
        )

        # Title
        worksheet.merge_range('A1:AF1', 'LAPORAN PAYMENT FILE VERIFICATION', s_title)
        worksheet.merge_range('A2:AF2', self.env.company.display_name.upper(), s_title)
        worksheet.merge_range(
            'A3:AF3',
            'Period {date_from} - {date_to}'.format(
                date_from=self.date_from.strftime('%d/%m/%Y'),
                date_to=self.date_to.strftime('%d/%m/%Y'),
            ),
            s_title,
        )

        # Information
        worksheet.merge_range('A5:B5', 'Business Unit:')
        worksheet.write('C5', 'LinkAja')
        worksheet.merge_range('A6:B6', 'Payment File Reference:')
        worksheet.merge_range('A7:B7', 'Date From:')
        worksheet.write('C7', self.date_from, s_title_date)
        worksheet.merge_range('A8:B8', 'Print Date:')
        worksheet.write('C8', fields.Date.today(), s_title_date)

        worksheet.write('U7', 'Date To:')
        worksheet.write('V7', self.date_to, s_title_date)
        worksheet.write('U8', 'Printed By:')
        worksheet.write('V8', self.env.user.display_name)

        # Table header
        headers = {
            'Payment File Reference': 18,
            'Payment File Status': 10,
            'Payment Reference': 18,
            'Payment Number': 18,
            'Payment Method': 12,
            'Bank': 10,
            'Disbursement Bank Account': 12,
            'Payment Date': 10,
            'Supplier/Party': 18,
            'Supplier/Party Number': 8,
            'Supplier/Party Site': 10,
            'Supplier/Party Bank Account': 8,
            'Payment Currency': 5,
            'Payment Status': 8,
            'Supplier Invoice Number': 18,
            'Document Invoice Number': 18,
            'Invoice Type': 8,
            'Pay Group': 5,
            'Invoice Date': 8,
            'Invoice Receive Date': 8,
            'Invoice Description': 10,
            'PO Number': 8,
            'AP Doc Type': 5,
            'FPJP Type': 5,
            'FPJP Number': 10,
            'FPJP Name': 10,
            'Justifikasi Number': 12,
            'Invoice Currency': 5,
            'Invoice Due Date': 5,
            'Invoice Amount': 10,
            'Amount to be Paid': 10,
            'Bank Instruction': 12,
        }

        for header_idx, (header, width) in enumerate(headers.items()):
            worksheet.set_column(10, header_idx, width)
            worksheet.write(10, header_idx, header, s_header)

        payment_states = dict(
            payments._fields['state']._description_selection(self.env)
        )

        # Table body
        for pay_idx, payment in enumerate(payments):
            pay_idx += 11
            partner_bank = ''
            if payment.partner_id.bank_ids:
                partner_bank = payment.partner_id.bank_ids[0].display_name

            for inv_idx, invoice in enumerate(payment.payment_invoice_ids):
                index = pay_idx + inv_idx
                worksheet.write(index, 0, payment.name, s_normal)
                worksheet.write(index, 1, payment_states.get(payment.state), s_normal)
                worksheet.write(index, 2, payment.name, s_normal)
                worksheet.write(index, 3, payment.name, s_normal)
                worksheet.write(index, 4, payment.payment_method_id.name, s_normal)
                worksheet.write(index, 5, payment.journal_id.bank_id.name, s_normal)
                worksheet.write(
                    index, 6, payment.journal_id.bank_account_id.acc_number, s_normal
                )
                worksheet.write(index, 7, payment.date, s_date)
                worksheet.write(index, 8, payment.partner_id.name, s_normal)
                worksheet.write(index, 9, payment.partner_id.ref, s_normal)
                worksheet.write(index, 10, payment.partner_id.city, s_normal)
                worksheet.write(index, 11, partner_bank, s_normal)
                worksheet.write(index, 12, payment.currency_id.name, s_normal)
                worksheet.write(index, 13, payment_states.get(payment.state), s_normal)
                worksheet.write(index, 14, invoice.name, s_normal)
                worksheet.write(index, 15, invoice.name, s_normal)
                worksheet.write(
                    index,
                    16,
                    invoice.move_id.invoice_type_id.name,
                    s_normal,
                )
                worksheet.write(index, 17, '', s_normal)
                worksheet.write(index, 18, invoice.date_invoice, s_date)
                worksheet.write(index, 19, invoice.date_accounting, s_date)
                worksheet.write(index, 20, invoice.description, s_normal)
                worksheet.write(index, 21, invoice.po_number, s_normal)
                worksheet.write(
                    index,
                    22,
                    invoice.move_id.fpjp_id and 'FPJP' or '',
                    s_normal,
                )
                worksheet.write(index, 23, invoice.move_id.fpjp_type_id.name, s_normal)
                worksheet.write(index, 24, invoice.move_id.fpjp_id.name, s_normal)
                worksheet.write(
                    index, 25, invoice.move_id.fpjp_id.description, s_normal
                )
                worksheet.write(
                    index,
                    26,
                    invoice.move_id.fpjp_id.justification_id.name,
                    s_normal,
                )
                worksheet.write(index, 27, invoice.currency_id.name, s_normal)
                worksheet.write(index, 28, invoice.move_id.invoice_date_due, s_date)
                worksheet.write(index, 29, invoice.amount, s_num)
                worksheet.write(index, 30, payment.amount, s_num)
                worksheet.write(index, 31, '', s_normal)

        workbook.close()
        output.seek(0)
        response.stream.write(output.read())
        output.close()
