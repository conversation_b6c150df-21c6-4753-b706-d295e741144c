<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_payment_verification_wizard_form" model="ir.ui.view">
        <field name="name">payment.verification.wizard.form</field>
        <field name="model">payment.verification.wizard</field>
        <field name="type">form</field>
        <field name="arch" type="xml">
            <form string="Payment Input Verification Report">
                <sheet>
                    <group>
                        <group>
                            <field name="date_from" string="Period" widget="daterange" options='{"end_date_field": "date_to", "always_range": "1"}' />
                            <field name="date_to" invisible="1" />
                        </group>
                    </group>
                    <footer>
                        <button name="action_export_xlsx" type="object" string="Export Report (xlsx)" class="oe_highlight"/>
                        <button string="Cancel" special="cancel" />
                    </footer>
                </sheet>
            </form>
        </field>
    </record>

    <record id="action_payment_verification_wizard" model="ir.actions.act_window">
        <field name="name">Payment Input Verification Report</field>
        <field name="res_model">payment.verification.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
</odoo>
