<?xml version="1.0" encoding="utf-8"?>
<odoo>

  <data>
  
    <record id="inherit_view_picking_form_id_inherit_module_stock" model="ir.ui.view">
      <field name="name">stock.picking.view.form.inherit</field>
      <field name="model">stock.picking</field>
      <field name="inherit_id" ref="stock.view_picking_form"/>
      <field name="arch" type="xml">
        <xpath expr="//div[@name='button_box']" position="inside">
          <button name="action_open_journal" type="object" class="oe_stat_button" icon="fa-book" invisible="picking_type_code != 'incoming'">
            <div class="o_stat_info">
              <span class="o_stat_text">Journal</span>
            </div>
          </button>
          <button name="action_open_journal_bill" type="object" class="oe_stat_button" icon="fa-book" invisible="picking_type_code != 'incoming'">
            <div class="o_stat_info">
              <span class="o_stat_text">Vendor Bill</span>
            </div>
          </button>
        </xpath>
        <xpath expr="//div[@class='o_row']" position="attributes">
          <attribute name="invisible" >picking_type_code == 'incoming'</attribute>
        </xpath>
        <xpath expr="//field[@name='location_dest_id'][@groups='stock.group_stock_multi_locations']" position="attributes">
            <attribute name="invisible">picking_type_code == 'incoming'</attribute>
        </xpath>
        <xpath expr="//field[@name='partner_id']" position="before">
          <field name="purchase_id" readonly="0" invisible="1" />
          <field name="purchase_dua_id" invisible="picking_type_code != 'incoming'" string="Source Document" domain="[('new_state', '=', 'open')]" readonly="state_fppr != 'draft'"/>
          <field name="partner_vendor_id" invisible="picking_type_code != 'incoming'" readonly="purchase_dua_id"/>
          <field name="buyer_employee_id" invisible="picking_type_code != 'incoming'"/>
          <field name="resquester_user_id" invisible="picking_type_code != 'incoming'" readonly="purchase_dua_id" force_save="1"/>
          <field name="created_user_id" invisible="picking_type_code != 'incoming'" readonly="purchase_dua_id" force_save="1"/>
          <field name="validity_date" readonly="1" force_save="1" invisible="picking_type_code != 'incoming'"/>
        </xpath>
        <xpath expr="//field[@name='partner_id']" position="attributes">
          <attribute name="invisible" >picking_type_code == 'incoming'</attribute>
        </xpath>
        <xpath expr="//field[@name='date_deadline']" position="before">
          <field name="transaction_date" invisible="picking_type_code != 'incoming'"/>
          <field name="accounting_date" invisible="picking_type_code != 'incoming'"/>
          <field name="accounting_period_id" invisible="picking_type_code != 'incoming'" readonly="purchase_dua_id"/>
          <field name="invoice_date" invisible="picking_type_code != 'incoming'" required="picking_type_code == 'incoming' and state_fppr == 'pending_approval'" readonly="state_fppr != 'draft'"/>
          <field name="nomor_invoice" invisible="picking_type_code != 'incoming'" required="1" readonly="state_fppr != 'draft'" />
          <field name="invoice_amount" invisible="picking_type_code != 'incoming'"/>
          <field name="name_attachment_nodin" invisible="1" />
          <field name="attachment_nodin" invisible="picking_type_code != 'incoming' or is_mandatory_attachment_nodin == False" required="is_mandatory_attachment_nodin == True" filename="name_attachment_nodin" widget="binary" readonly="state_fppr != 'draft'"/>
          <field name="is_mandatory_attachment_nodin" invisible="1"/>
          <field name="attachment_invoice" invisible="picking_type_code != 'incoming'" filename="name_attachment_invoice" widget="binary" required="state == 'draft'" readonly="state_fppr != 'draft'" />
          <field name="name_attachment_invoice" invisible="1" />
          <field name="cip_id" invisible="picking_type_code != 'incoming'" readonly="1" />
        </xpath>
        <xpath expr="//field[@name='origin']" position="attributes">
          <attribute name="invisible" >picking_type_code == 'incoming'</attribute>
        </xpath>
        <xpath expr="//field[@name='picking_type_id']" position="attributes">
          <attribute name="readonly">1</attribute>
        </xpath>
        <xpath expr="//page[@name='operations']" position="replace">
          <page name="operations_gr" string="Operations" invisible="picking_type_code != 'incoming'">
            <field name="move_ids_without_package" mode="list,kanban" widget="stock_move_one2many" context="{'default_company_id': company_id, 'default_date': scheduled_date, 'default_date_deadline': date_deadline, 'picking_type_code': picking_type_code, 'default_picking_id': id, 'form_view_ref': 'stock.view_stock_move_operations', 'address_in_id': partner_id, 'default_picking_type_id': picking_type_id, 'default_location_id': location_id, 'default_location_dest_id': location_dest_id, 'default_partner_id': partner_id}" add-label="Add a Product">
              <list decoration-muted="scrapped == True or state == 'cancel' or (state == 'done' and is_locked == True)" string="Stock Moves" editable="bottom" create="0">
                <field name="product_tmpl_id" string="Product" forca_save="1" required="1"/>
                  <field name="product_id" string="Product Variant" context="{'default_is_storable': True}" required="1" readonly="(state != 'draft' and not additional) or move_lines_count &gt; 0" force_save="1" domain="[('product_tmpl_id', '=', product_tmpl_id), ('is_product_variant', '=', True)]"/>
                  <field name="asset_location_id" required="product_id.is_asset == True" />
                  <field name="location_id" required="0" column_invisible="1"/>
                  <field name="rkap_code_id" column_invisible="1"/>
                  <field name="rkap_code" />
                  <field name="product_uom_qty" readonly="1" forca_save="1"/>
                  <field name="quantity"  />
                  <field name="product_uom_id" />
                  <field name="unit_price" column_invisible="1"/>
                  <field name="currency_id" force_save='1'/>
                  <field name="ammount_currency" options="{'currency_field': 'currency_id'}" string="Amount" />
                  <field name="purchase_order_line_id" column_invisible="1"/>
              </list>
            </field>
          </page>
          <page name="fppr" string="FPPR" invisible="picking_type_code != 'incoming'">
            <group >
              <field name="nilai_akhir" />
              <field name="description" />
            </group>
            <field name="assesment_purchase_line_ids" >
              <list create="0" editable="bottom" delete="0">
                <field name="assesment_aspect" />
                <field name="score_penilaian" required='1' readonly="parent.state_fppr != 'draft'"/>
                <field name="persentasi_bobot" />
                <field name="bobot_nilai" />
                <field name="interpretasi_penilaian" />
                <field name="justifikasi_penilaian" required='1' />
              </list>
            </field>
          </page>
          <page name="approval_gr" string="Approval GR">
            <field name="approval_gr_line_ids" >
              <list editable="bottom">
                <field name="sequence" />
                <field name="approval_name" />
                <field name="approval_position" />
                <field name="approval_status" />
                <field name="approval_date" />
                <field name="approval_note" />
                <field name="reassign_to" />
                <field name="approval_status_dua" />
                <field name="approval_date_dua" />
                <field name="approval_note_dua" />
                <field name="approval_note_dua" />
              </list>
            </field>
          </page>
          <page name="operations" string="Operations" invisible="picking_type_code == 'incoming'">
            <field name="move_ids_without_package" mode="list,kanban" widget="stock_move_one2many" readonly="state == 'done' and is_locked" context="{'default_company_id': company_id, 'default_date': scheduled_date, 'default_date_deadline': date_deadline, 'picking_type_code': picking_type_code, 'default_picking_id': id, 'form_view_ref': 'stock.view_stock_move_operations', 'address_in_id': partner_id, 'default_picking_type_id': picking_type_id, 'default_location_id': location_id, 'default_location_dest_id': location_dest_id, 'default_partner_id': partner_id}" add-label="Add a Product">
              <list decoration-muted="scrapped == True or state == 'cancel' or (state == 'done' and is_locked == True)" string="Stock Moves" editable="bottom">
                <field name="company_id" column_invisible="True"/>
                <field name="picking_id" column_invisible="True"/>
                <field name="name" column_invisible="True"/>
                <field name="state" readonly="0" column_invisible="True"/>
                <field name="picking_type_id" column_invisible="True"/>
                <field name="move_line_ids" column_invisible="True"/>
                <field name="location_id" column_invisible="True"/>
                <field name="location_dest_id" column_invisible="True"/>
                <field name="partner_id" column_invisible="True" readonly="state == 'done'"/>
                <field name="scrapped" column_invisible="True"/>
                <field name="picking_code" column_invisible="True"/>
                <field name="show_details_visible" column_invisible="True"/>
                <field name="additional" column_invisible="True"/>
                <field name="move_lines_count" column_invisible="True"/>
                <field name="is_locked" column_invisible="True"/>
                <field name="product_uom_category_id" column_invisible="True"/>
                <field name="is_storable" column_invisible="True"/>
                <field name="has_tracking" column_invisible="True"/>
                <field name="product_id" context="{'default_is_storable': True}" required="1" readonly="(state != 'draft' and not additional) or move_lines_count &gt; 0" force_save="1"/>
                <field name="location_final_id" optional="hide" groups="stock.group_stock_multi_locations"/>
                <field name="description_picking" string="Description" optional="hide"/>
                <field name="date" optional="hide"/>
                <field name="date_deadline" optional="hide"/>
                <field name="is_quantity_done_editable" column_invisible="True"/>
                <field name="show_quant" column_invisible="True"/>
                <field name="show_lots_text" column_invisible="True"/>
                <field name="show_lots_m2o" column_invisible="True"/>
                <field name="is_initial_demand_editable" column_invisible="True"/>
                <field name="display_import_lot" column_invisible="True"/>
                <field name="picking_type_entire_packs" column_invisible="True"/>
                <field name="product_packaging_id" groups="product.group_stock_packaging" context="{'default_product_id': product_id}" readonly="not product_id"/>
                <field name="product_uom_qty" string="Demand" readonly="not is_initial_demand_editable"/>
                <button type="object" name="action_product_forecast_report" title="Forecast Report" icon="fa-area-chart" invisible="not product_id or product_uom_qty == 0 or quantity == 0 and forecast_availability &lt;= 0 or (parent.picking_type_code == 'outgoing' and state != 'draft')"/>
                <button type="object" name="action_product_forecast_report" title="Forecast Report" icon="fa-area-chart text-danger" invisible="not product_id or product_uom_qty == 0 or quantity &gt; 0 or forecast_availability &gt; 0 or (parent.picking_type_code == 'outgoing' and state != 'draft')"/>
                <field name="forecast_expected_date" column_invisible="True"/>
                <field name="forecast_availability" string="Forecast" optional="hide" column_invisible="parent.state in ('draft', 'done') or parent.picking_type_code != 'outgoing'" widget="forecast_widget"/>
                <field name="product_qty" readonly="1" column_invisible="True"/>
                <field name="quantity" string="Quantity" readonly="not is_quantity_done_editable" column_invisible="parent.state=='draft'" decoration-danger="product_uom_qty and quantity &gt; product_uom_qty and parent.state not in ['done', 'cancel']"/>
                <field name="product_uom" readonly="state != 'draft' and not additional" options="{'no_open': True, 'no_create': True}" string="Unit" groups="uom.group_uom"/>
                <field name="product_uom" groups="!uom.group_uom" column_invisible="True"/>
                <field name="picked" optional="hide" column_invisible="parent.state=='draft'"/>
                <field name="lot_ids" widget="many2many_tags" column_invisible="parent.state == 'draft'" groups="stock.group_production_lot" invisible="not show_details_visible or has_tracking == 'none'" optional="hide" options="{'create': [('parent.use_create_lots', '=', True)]}" context="{'default_company_id': company_id, 'default_product_id': product_id, 'active_picking_id': parent.id}" domain="[('product_id','=',product_id)]"/>
              </list>
          </field>
          </page>
        </xpath>
        <!-- <xpath expr="//field[@name='state'][1]" position="replace">
          
          <field name="state_fppr" widget="statusbar" invisible="picking_type_code != 'incoming'" statusbar_visible="draft,pending_approval,ap_invoice,return,cancel"/>
        </xpath> -->
        <xpath expr="//header" position="replace">
          <header>
            <button name="action_confirm" invisible="state != 'draft' or picking_type_code == 'incoming'" string="Mark as Todo" type="object" class="oe_highlight" groups="base.group_user" data-hotkey="q"/>
            <button name="action_assign" invisible="not show_check_availability or picking_type_code == 'incoming'" string="Check Availability" type="object" class="oe_highlight" groups="base.group_user" data-hotkey="w"/>
            <button name="button_validate" invisible="state in ('draft', 'confirmed', 'done', 'cancel') or picking_type_code == 'incoming'" string="Validate" type="object" class="oe_highlight" groups="stock.group_stock_user" data-hotkey="v"/>
            <button name="button_validate" invisible="state in ('waiting', 'assigned', 'done', 'cancel') or picking_type_code == 'incoming'" string="Validate" type="object" groups="stock.group_stock_user" class="o_btn_validate" data-hotkey="v"/>
            <widget name="signature" string="Sign" highlight="1" invisible="not id or picking_type_code != 'outgoing' or state != 'done' or picking_type_code == 'incoming'" full_name="partner_id" groups="stock.group_stock_sign_delivery"/>
            <widget name="signature" string="Sign" invisible="not id or picking_type_code != 'outgoing' or state == 'done' or picking_type_code == 'incoming'" full_name="partner_id" groups="stock.group_stock_sign_delivery"/>
            <button name="do_print_picking" string="Print" groups="stock.group_stock_user" type="object" invisible="state != 'assigned' or picking_type_code == 'incoming'" data-hotkey="o"/>
            <button name="%(stock.action_report_delivery)d" string="Print" invisible="state != 'done' or picking_type_code == 'incoming'" type="action" groups="base.group_user" data-hotkey="o"/>
            <button name="%(stock.act_stock_return_picking)d" string="Return" invisible="state != 'done' or picking_type_code == 'incoming'" type="action" groups="base.group_user" data-hotkey="k"/>
            <field name="state" widget="statusbar" invisible="picking_type_code == 'incoming'" statusbar_visible="draft,confirmed,assigned,done"/>
            <button name="action_cancel" invisible="state not in ('assigned', 'confirmed', 'draft', 'waiting') or picking_type_code == 'incoming'" string="Cancel" groups="base.group_user" type="object" confirm="Are you sure you want to cancel this transfer?" data-hotkey="x"/>
            <!-- Button for FPPR -->
            <button string="Submit" name="action_button_submit" type="object" invisible="state_fppr != 'draft' or picking_type_code != 'incoming'" class="oe_highlight" />
            <button string="Approve" name="action_approve" type="object" invisible="state_fppr != 'pending_approval' or picking_type_code != 'incoming'" class="oe_highlight" />
            <button string="Reject" name="action_reject" type="object" invisible="state_fppr != 'pending_approval' or picking_type_code != 'incoming'" class="oe_highlight" />
            <!-- <button string="Return of Documents" name="action_button_return_of_documents" invisible="picking_type_code != 'incoming'" type="object" />
            <button string="Return of Goods" name="%(stock.act_stock_return_picking)d" type="action" invisible="picking_type_code != 'incoming'" /> -->
            <button string="Return" name="action_button_return_to_draft" invisible="state_fppr != 'pending_approval' or picking_type_code != 'incoming'" type="object" />
            <button string="Cancel" name="action_button_cancel" type="object" invisible="picking_type_code != 'incoming' or state_fppr in ['ap_invoice','cancel']" />
            <button string="Withdraw Approval" name="action_button_withdraw_approval" type="object" invisible="state_fppr != 'ap_invoice' or picking_type_code != 'incoming'" />
            <field name="state_fppr" widget="statusbar" invisible="picking_type_code != 'incoming'" statusbar_visible="draft,pending_approval,ap_invoice,return,cancel"/>
          </header>
        </xpath>
        <xpath expr="//label[@for='scheduled_date']" position="attributes">
          <attribute name="invisible">1</attribute>
        </xpath>
        <xpath expr="//div[hasclass('o_row')]" position="attributes">
          <attribute name="invisible">1</attribute>
        </xpath>
      </field>
    </record>
    
    <record id="purchase_fppr_invoice_tree_view_inherit" model="ir.ui.view">
        <field name="name">purchase.fppr.move.list.view.inherit</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_invoice_tree" />
        <field name="arch" type="xml">
            <field name="invoice_origin" position="after">
                <field name="payment_reference" invisible="move_type not in ['in_invoice']"/>
            </field>
            <xpath expr="//field[@name='status_in_payment']" position="attributes">
              <attribute name="optional">hide</attribute>
            </xpath>
            <field name="status_in_payment" position="replace">
              <field name="total_amount_residual" />
              <field name="state" />
            </field>
        </field>
    </record>
	<record id="purchase_order_form_inherit_generate_gr" model="ir.ui.view">
		<field name="name">purchase.order.form.generate.gr</field>
		<field name="model">purchase.order</field>
		<field name="inherit_id" ref="purchase.purchase_order_form"/>
		<field name="arch" type="xml">
			<header position="inside">
				<button name="action_generate_gr"
						type="object"
						string="Generate GR"
						class="oe_highlight"
						invisible="new_state != 'open'"/>
			</header>
		</field>
	</record>
	<record id="view_purchase_order_form_inherit_gr_button" model="ir.ui.view">
		<field name="name">purchase.order.form.inherit.gr.button</field>
		<field name="model">purchase.order</field>
		<field name="inherit_id" ref="purchase.purchase_order_form"/>
		<field name="arch" type="xml">
			<xpath expr="//div[@name='button_box']" position="inside">
				<button name="action_open_gr"
						type="object"
						class="oe_stat_button"
						icon="fa-truck"
						invisible="new_state not in ['open','closed','done']">
					<field name="gr_count" widget="statinfo" string="Goods Receipts"/>
				</button>
			</xpath>
		</field>
	</record>
	
  </data>
  

</odoo>
