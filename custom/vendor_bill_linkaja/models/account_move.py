# -*- coding: utf-8 -*-

from datetime import datetime
from dateutil.relativedelta import relativedelta
import logging
from markupsafe import Markup
from contextlib import ExitStack, contextmanager

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)


class AccountMove(models.Model):
    _inherit = 'account.move'
    
    def _get_default_domain_partner(self):
        if self._context.get('default_move_type') == 'in_invoice':
            return self.env['res.partner'].search([('company_id', 'in', (False, self.env.user.company_id.id)), ('parent_id', '=', False), ('vendor_state', '=', 'spendauthorized')])
        elif self._context.get('default_move_type') == 'out_invoice':
            return self.env['res.partner'].search([('company_id', 'in', (False, self.env.user.company_id.id)), ('parent_id', '=', False), ('state', '=', 'active')])
        else:
            return self.env['res.partner'].search([('company_id', 'in', (False, self.env.user.company_id.id))])

    validation_state = fields.Selection([
        ('draft', 'Draft'),
        ('submit', 'Submit'),
        ('verification', 'Tax Verification'),
        ('validate', 'Validate Treasury'),
        ('initiate_approval', 'Initiate Approval'),
        ('pending', 'Pending Approval'),
        ('approve', 'Approve'),
        ('rejected', 'Rejected'),
        ('cancel', 'Cancel'),
    ], string='Validation State', default="draft", copy=False,tracking=True)
    
    vendor_contact_id = fields.Many2one('res.partner', string='Vendor Contact',domain="[('is_company', '=', False), ('parent_id', '=', partner_id)]",tracking=True)
    contact_email = fields.Char(string='Contact Email', related='vendor_contact_id.email')
    vendor_email = fields.Char(string='Vendor Email', related='partner_id.email',tracking=True,store=True)
    vendor_name = fields.Char(string='Vendor Name', related='partner_id.email',tracking=True,store=True)
    address = fields.Text(string='Address', compute="_get_address", store=True)
    
    
    source = fields.Selection(selection=[
        ('purchase', 'Purchase Order'),
        ('fpjp', 'FPJP'),
        ('manual', 'Manual'),
        ],string='Source',tracking=True, compute='_compute_source', store=True)
    
    purchase_order_id = fields.Many2one('purchase.order', string='PO Number',tracking=True)
    picking_id = fields.Many2one('stock.picking',string='GR Number',tracking=True)
    fpjp_id = fields.Many2one('account.fpjp',string='FPJP Number',tracking=True)
    fpjp_type_id = fields.Many2one('account.fpjp.type', string='Type',related='fpjp_id.fpjp_type_id',store=True)
    fpjp_source = fields.Selection([
        ('justification', 'Justification'),
        ('non_justification', 'Non Justification'),
        ('manual', 'Manual'),
    ], string='FPJP Source', related='fpjp_type_id.source', store=True)
    fpjp_source_transaction = fields.Selection([
        ('justification', 'Justification'),
        ('non_justification', 'Non Justification'),
    ], string='Source', related='fpjp_id.source', store=True)
    tax_verification_type = fields.Selection(related='fpjp_type_id.tax_verification_type', string='Tax Verification Type',store=True)
    
    return_note = fields.Text(string='Return Note',tracking=True)
    reject_note = fields.Text(string='Reject Note',tracking=True)
    
    submitter_id = fields.Many2one('hr.employee', string='User',default=lambda self:self.env.user.employee_id,tracking=True)
    requestor_id = fields.Many2one('hr.employee', string='Requestor Name',default=lambda self:self.env.user.employee_id,tracking=True)
    
    unit_id = fields.Many2one('hr.department', string='Unit',tracking=True)
    group_department_id = fields.Many2one('hr.department', string='Group', compute='_compute_group', store=True)
    
    general_budget_id = fields.Many2one('account.budget.post', string='General Budget',tracking=True)
    budgetary_position_id = fields.Many2one('crossovered.budget.lines', string='RKAP',tracking=True)
    rkap_code = fields.Char(string='Kode RKAP',tracking=True,related='budgetary_position_id.rkap_code',store=True)
    rkap_category_id = fields.Many2one('account.rkap.category', 'RKAP Category',related='budgetary_position_id.rkap_category_id')
    rkap_type_id = fields.Many2one('account.rkap.type', 'RKAP Type',related='budgetary_position_id.rkap_type_id')
    

    
    pay_group = fields.Selection([
        ('in-house', 'In-House'),
        ('others', 'Others'),
        ('non', 'Non MT 100'),
        ('digi_bni', 'DIGIPos BNI'),
        ('digi_others', 'DIGIPos Others'),
    ], string=f'Pay Group', default='in-house', copy=False,tracking=True)
    
    project_id = fields.Many2one('phase.project.cip', string='Project', tracking=True)
    account_document_line_ids = fields.One2many('account.document.line', 'move_id', string='Account Document Line')

    group_id = fields.Many2one(
        comodel_name='hr.department',
        string='Group',
        domain="[('department_type', '=', '3_group')]",
        context={'hierarchical_naming': False},
        tracking=True
    )
    
    amount_discount_total = fields.Monetary(
        string="Amount Discount",
        compute='_compute_amount_discount_total',
        currency_field='currency_id',
        store=True
    )

    dpp_amount = fields.Monetary(
        string='Dasar Pengenaan Pajak',
        compute='_compute_dpp_amount',
        store=True,
        currency_field='currency_id'
    )
    dpp_nilai_lain = fields.Monetary(
        string='DPP Nilai Lain',
        compute='_compute_dpp_nilai_lain',
        currency_field='currency_id',
        readonly=True,
        store=True
    )

    label_ppn = fields.Char(string='PPN', default='PPN')
    label_pph = fields.Char(string='PPH', default='PPH')
    
    tax_amount = fields.Monetary(string='PPn', compute='_compute_tax_amount', store=True, precompute=True)
    tax_amount_pph = fields.Monetary(string='PPh', compute='_compute_tax_amount', store=True, precompute=True)
    total_amount = fields.Monetary(string='Total', compute='_compute_total_amount', readonly=True, store=True)
    total_amount_residual = fields.Monetary(string='Total', compute='_compute_total_amount', readonly=True, store=True)
    is_fpjp_bill = fields.Boolean(compute='_compute_readonly_status')
    domain_partner_ids = fields.One2many('res.partner', string='Domain Partner', compute='_compute_domain_partner', default=_get_default_domain_partner)
    state = fields.Selection(
        selection_add=[
            ('tax', 'Tax'),
            ('posted', 'Posted'),
        ],
        ondelete={
            'tax': 'set default',
            'posted': 'set default',
        }
    )


    has_dpp_nilai_lain = fields.Boolean(
        string="Has DPP Nilai Lain",
        compute='_compute_has_dpp_nilai_lain',
        store=True
    )

    is_project_readonly = fields.Boolean(string="Project Readonly", compute="_compute_project_readonly", store=True)

    is_validate_before = fields.Boolean('Is Validate Before')
    is_initiate_before = fields.Boolean('Is Initiate Before')


    @api.depends('invoice_line_ids', 'invoice_line_ids.product_template_id.is_cip', 'invoice_line_ids.product_id.is_cip')
    def _compute_project_readonly(self):
        for rec in self:
            lines = rec.invoice_line_ids.filtered(lambda x: x.product_template_id.is_cip or x.product_id.is_cip)
            if lines:
                rec.is_project_readonly = True
            else:
                rec.is_project_readonly = False


    def _compute_payments_widget_to_reconcile_info(self):
        # for move in self:
        #     move.invoice_outstanding_credits_debits_widget = False
        #     move.invoice_has_outstanding = False

        #     if move.state != 'posted' \
        #             or move.payment_state not in ('not_paid', 'partial') \
        #             or not move.is_invoice(include_receipts=True):
        #         continue

        #     pay_term_lines = move.line_ids\
        #         .filtered(lambda line: line.account_id.account_type in ('asset_receivable', 'liability_payable'))

        #     domain = [
        #         ('account_id', 'in', pay_term_lines.account_id.ids),
        #         ('parent_state', '=', 'posted'),
        #         ('partner_id', '=', move.commercial_partner_id.id),
        #         ('reconciled', '=', False),
        #         '|', ('amount_residual', '!=', 0.0), ('amount_residual_currency', '!=', 0.0),
        #     ]

        #     payments_widget_vals = {'outstanding': True, 'content': [], 'move_id': move.id}

        #     if move.is_inbound():
        #         domain.append(('balance', '<', 0.0))
        #         payments_widget_vals['title'] = _('Outstanding credits')
        #     else:
        #         domain.append(('balance', '>', 0.0))
        #         payments_widget_vals['title'] = _('Outstanding debits')

        #     for line in self.env['account.move.line'].search(domain):

        #         if line.currency_id == move.currency_id:
        #             # Same foreign currency.
        #             amount = abs(line.amount_residual_currency)
        #         else:
        #             # Different foreign currencies.
        #             amount = line.company_currency_id._convert(
        #                 abs(line.amount_residual),
        #                 move.currency_id,
        #                 move.company_id,
        #                 line.date,
        #             )

        #         if move.currency_id.is_zero(amount):
        #             continue

        #         payments_widget_vals['content'].append({
        #             'journal_name': line.ref or line.move_id.name,
        #             'amount': amount,
        #             'currency_id': move.currency_id.id,
        #             'id': line.id,
        #             'move_id': line.move_id.id,
        #             'date': fields.Date.to_string(line.date),
        #             'account_payment_id': line.payment_id.id,
        #         })

        #     if not payments_widget_vals['content']:
        #         continue
        super(AccountMove, self)._compute_payments_widget_to_reconcile_info()
        for move in self:
            move.invoice_outstanding_credits_debits_widget = False
            move.invoice_has_outstanding = False

    @api.depends('requestor_id', 'line_ids')
    def _compute_group(self):
        for rec in self:
            if rec.move_type == 'in_invoice':
                rec.group_department_id = rec.requestor_id.department_id.get_ancestor_department_by_type(department_type='3_group')
            else:
                rec.group_department_id = False

            if rec.move_type == 'in_invoice':
                for line in rec.line_ids:
                    line.group_id = line.move_id.group_department_id.id

    @api.depends('invoice_line_ids.tax_ids')
    def _compute_has_dpp_nilai_lain(self):
        for record in self:
            if record.move_type == 'in_invoice':
                has_dpp_nilai_lain = False
                for line in record.invoice_line_ids:
                    for tax in line.tax_ids:
                        if tax.dpp_nilai_lain > 0:
                            has_dpp_nilai_lain = True
                record.has_dpp_nilai_lain = has_dpp_nilai_lain
            else:
                record.has_dpp_nilai_lain = False
    

    @api.depends('source', 'state')
    def _compute_readonly_status(self):
        for record in self:
            record.is_fpjp_bill = record.source == 'fpjp'

    @api.depends('invoice_line_ids.price_subtotal_bill', 'invoice_line_ids.amount_discount', 'invoice_line_ids.tax_ids', 'tax_amount', 'tax_amount_pph')
    def _compute_total_amount(self):
        for move in self:
            total_subtotal = sum(line.price_subtotal_bill for line in move.invoice_line_ids)
            total_discount = sum(line.amount_discount for line in move.invoice_line_ids)
            total_amount = (total_subtotal - total_discount) + move.tax_amount + move.tax_amount_pph
            move.total_amount = total_amount
            move.total_amount_residual = sum(line.amount_residual for line in move.line_ids)

    @api.depends('invoice_line_ids.price_subtotal_bill', 'invoice_line_ids.amount_discount', 'invoice_line_ids.tax_ids')
    def _compute_tax_amount(self):
        for invoice in self:
            # total_tax = 0.0
            # for line in invoice.invoice_line_ids:
            #     base = line.price_subtotal_bill - line.amount_discount
            #     taxes = line.tax_ids.compute_all(base, invoice.currency_id, 1.0, product=line.product_id, partner=invoice.partner_id)
            #     total_tax += sum(tax['amount'] for tax in taxes['taxes'])
            # invoice.tax_amount = total_tax

            ppn = 0
            pph = 0
            label_ppn = 'PPN'
            label_pph = 'PPH'
            for line in invoice.invoice_line_ids:
                for tax in line.tax_ids:
                    if tax.dpp_nilai_lain > 0:
                        ppn = invoice.dpp_nilai_lain * tax.ppn_coretax / 100
                        label_ppn = tax.tax_group_id.name
                    else:
                        amount_pph = (line.price_subtotal_bill - line.amount_discount) * tax.amount / 100
                        pph += amount_pph
                        label_pph = tax.tax_group_id.name

            invoice.tax_amount = ppn
            invoice.tax_amount_pph = pph
            invoice.label_ppn = label_ppn
            invoice.label_pph = label_pph
            
    @api.depends('invoice_line_ids.price_subtotal_bill', 'amount_discount_total')
    def _compute_dpp_amount(self):
        for move in self:
            total_subtotal = sum(move.invoice_line_ids.mapped('price_subtotal_bill')) or 0.0
            amount_discount = move.amount_discount_total or 0.0
            move.dpp_amount = total_subtotal - amount_discount
            
    @api.depends('invoice_line_ids.amount_discount')
    def _compute_amount_discount_total(self):
        for move in self:
            move.amount_discount_total = sum(move.invoice_line_ids.mapped('amount_discount'))
            
    @api.depends('dpp_amount')
    def _compute_dpp_nilai_lain(self):
        for record in self:
            dpp_lain = 1
            dpp = 0
            for line in record.invoice_line_ids:
                for tax in line.tax_ids:
                    if tax.dpp_nilai_lain > 0:
                        dpp_lain = tax.dpp_nilai_lain
                        dpp += line.price_subtotal_bill - line.amount_discount

            record.dpp_nilai_lain = dpp * dpp_lain
    
    # def action_post(self):
    #     for move in self:
    #         if move.move_type == 'in_invoice' and move.name in ('/', False):
    #             if move.source  != 'fpjp':
    #                 move.name = self.env['ir.sequence'].next_by_code('account.move.vendor') or '/'
    #             elif move.source  == 'fpjp':
    #                 move.name = self.env['ir.sequence'].next_by_code('account.move.fpjp') or '/'
                    

    #     return super(AccountMove, self).action_post()
    
    
    def _get_starting_sequence(self):
        self.ensure_one()
        if self.is_fpjp_bill:
            return self.env['ir.sequence'].next_by_code('account.move.fpjp') or '/'
        return super(AccountMove, self)._get_starting_sequence()
    
    @api.constrains('invoice_line_ids')
    def _check_invoice_line_not_empty(self):
        for record in self:
            if record.move_type == 'in_invoice' and not record.invoice_line_ids:
                raise ValidationError(_("Invoice Line tidak boleh kosong untuk Vendor Bill."))
            

    def button_draft(self):
        res = super().button_draft()
        if self.move_type == 'in_invoice':
            if self.state == 'draft' and self.validation_state != 'draft':
                self.write({'validation_state': 'draft'})
        return res
    
    def button_draft_invoice(self):
        for move in self:
            move.button_draft()

    def button_cancel(self):
        res = super().button_cancel()
        if self.move_type == 'in_invoice':
            if self.state == 'cancel' and self.validation_state != 'rejected':
                self.write({'validation_state': 'cancel'})
        return res

    def action_send_message(self):
        link = self._get_html_link(title=self.name)
        taxes = ",".join([ tax.name for tax in self.invoice_line_ids.mapped('tax_ids')])
        subject = f"[Action Required] Pengecekan Pajak pada Vendor Bill – {self.display_name}"
        tax_department = self.env['hr.department'].search([
            ('name', 'ilike', 'Accounting & Tax Unit'),
        ], limit=1)
        config_setting_params = self.env['res.config.settings'].sudo().get_values()
        recipient_ids = config_setting_params.get('res_partner_ids') if config_setting_params.get('res_partner_ids') else []
        
        body = Markup(f"""
        <p>Dear {tax_department.name}, </p>
        <br/>
        <br/>
        <p>Mohon dilakukan pengecekan terhadap aspek perpajakan pada vendor bill berikut:</p>
        <br/>
        &#128204; Nomor Vendor Bill: {link}<br/>
        &#128204; Nilai Invoice: {self.amount_total}<br/>
        &#128204; Jenis Pajak: {taxes}<br/>
        <br/>
        <br/>
        <p>{config_setting_params.get('body') if config_setting_params.get('body') else ''}</p>
        <br/>
        <br/>

        {self.env.user.department_id.name}
        """)
        
        self.env['send.bill.message.queue'].create({
            'bill_id': self.id,
            'subject': subject,
            'body': str(body),
            'partner_ids': recipient_ids,
            'move_type': 'bill',
        })

    def send_message_email_tax(self):
        link = self._get_html_link(title=self.name)
        subject = f"[Action Required] Pengecekan Pajak pada Customer Invoices – {self.display_name}"
        tax_department = self.env['hr.department'].search([
            ('name', 'ilike', 'Accounting & Tax Unit'),
        ], limit=1)
        try:
            tax_group = self.env.ref('customer_ext_linkaja.group_user_tax')
        except ValueError:
            raise UserError("Grup 'User Tax' tidak ditemukan. Periksa apakah modul sudah terinstall dengan benar.")

        # Ambil semua partner dari user yang tergabung di grup
        recipient_ids = tax_group.users.mapped('partner_id.id')

        body = Markup(f"""
        <p>Dear {tax_department.name}, </p>
        <br/>
        <br/>
        <p>Mohon dilakukan pengecekan terhadap aspek perpajakan pada customer Invoice berikut:</p>
        <br/>
        &#128204; Nomor Customer Invoice: {link}<br/>
        &#128204; Tolong Mengisi ketiga field dicoretax yaitu: <br/>
        &#128204; Kode Transaksi, Kode Fasilitas, Kode Cap Fasilitas<br/>
        <br/>
        <br/>
        <br/>
        {self.env.user.department_id.name}
        """)

        self.write({'state': 'tax'})

        self.env['send.bill.message.queue'].create({
            'bill_id': self.id,
            'subject': subject,
            'body': str(body),
            'partner_ids': recipient_ids,
            'move_type': 'invoice',
        })
        
    
    def action_submit(self):
        self.validation_state = 'submit'

    def action_verification(self):
        for record in self:
            if record.move_type == 'in_invoice':
                if record.source in ['manual', 'fpjp']:
                    for line in record.invoice_line_ids:
                        if not line.account_id:
                            raise ValidationError('Account tidak boleh kosong')
                        if line.quantity == 0:
                            raise ValidationError('Quantity tidak boleh 0')
                else:
                    record.invoice_date = datetime.today()
                for line in record.invoice_line_ids:
                    if not line.account_id:
                        raise UserError(_("Account is required for all lines in Vendor Bill!"))
                    if record.source == 'fpjp':
                        if line.price_total_currency_bill > line.fpjp_line_amount_total:
                            raise UserError(_(
                                "Line total amount (%s) cannot exceed FPJP line amount (%s) for product '%s'."
                            ) % (
                                line.price_total_currency_bill,
                                line.fpjp_line_amount_total,
                                line.product_template_id.name or 'Undefined Product'
                            ))
                        
            validation_state = 'verification'
                    
            if record.fpjp_id:
                if record.fpjp_type_id.tax_verification_type == 'tax_verification':
                    # record.write({'validation_state': 'verification'})
                    validation_state = 'verification'
                    # self.action_send_message()
                else:
                    validation_state = 'validate'
                    # record.write({'validation_state': 'validate'})
            else:
                # record.write({'validation_state': 'verification'})
                # self.action_send_message()

                check_taxes = record.line_ids.filtered(lambda x: x.tax_ids)
                if not check_taxes:
                    validation_state = 'validate'
                    # record.write({'validation_state': 'validate'})
                else:
                    validation_state = 'verification'
                    # record.write({'validation_state': 'verification'})

            if validation_state == 'verification':
                record.write({'validation_state': 'verification'})
                record.action_send_message()
            else:
                record.write({'validation_state': 'validate', 'is_validate_before': True})

    def action_validate(self):
        for record in self:
            record.write({'validation_state': 'validate', 'is_validate_before': True})

    def action_initiate_approval(self):
        for record in self:
            record.write({'validation_state': 'initiate_approval', 'is_initiate_before': True})
    
    def action_initiate(self):
        for record in self:
            record.write({'validation_state': 'pending'})
    
    def action_approve(self):
        for record in self:
            record.action_post()
            record.write({'validation_state': 'approve'})
    
    def action_return_to_submitter(self):
        action = self.env['ir.actions.actions']._for_xml_id('vendor_bill_linkaja.action_account_move_return_wizard')
        action['context'] = {
            'default_move_id': self.id,
        }
        return action
        
    def action_return_to_tax(self):
        for record in self:
            validation_state = 'verification'
                        
            if record.fpjp_id:
                if record.fpjp_type_id.tax_verification_type == 'tax_verification':
                    # record.write({'validation_state': 'verification'})
                    validation_state = 'verification'
                    # self.action_send_message()
                else:
                    validation_state = 'validate'
                    # record.write({'validation_state': 'validate'})
            else:
                # record.write({'validation_state': 'verification'})
                # self.action_send_message()

                check_taxes = record.line_ids.filtered(lambda x: x.tax_ids)
                if not check_taxes:
                    validation_state = 'validate'
                    # record.write({'validation_state': 'validate'})
                else:
                    validation_state = 'verification'
                    # record.write({'validation_state': 'verification'})

            if validation_state == 'verification':
                record.write({'validation_state': 'verification'})
            else:
                record.write({'validation_state': 'draft'})
        
    def action_withdraw_approval(self):
        for record in self:
            record.write({'validation_state': 'validate'})
            record.write({'state': 'draft'})
        
    def action_reject(self):
        action = self.env['ir.actions.actions']._for_xml_id('vendor_bill_linkaja.action_account_move_reject_wizard')
        action['context'] = {
            'default_move_id': self.id,
        }
        return action

    def action_set_to_draft(self):
        for record in self:
            record.write({'validation_state': 'draft', 'state': 'draft'})

    @api.onchange('requestor_id')
    def _onchange_requestor_id(self):
        if self.requestor_id and self.requestor_id.department_id:
            self.unit_id = self.requestor_id.department_id.get_ancestor_department_by_type(department_type='4_unit')
            self.group_department_id = self.requestor_id.department_id.get_ancestor_department_by_type(department_type='3_group')



    @api.depends('partner_id')
    def _get_address(self):
        for record in self:
            if record.partner_id:
                address = record.partner_id._display_address(without_company=True)
                record.address = address
            else:
                record.address = ''

    @api.depends('purchase_order_id', 'fpjp_id')
    def _compute_source(self):
        for record in self:
            if record.purchase_order_id:
                record.source = 'purchase'
            elif record.fpjp_id:
                record.source = 'fpjp'
            else:
                record.source = 'manual'

    def create(self, vals_list):
        result = super().create(vals_list)
        # for move in result:
        #     if move.move_type == 'in_invoice' and move.name in ('/', False):
        #         if move.source  == 'fpjp':
        #             move.name = self.env['ir.sequence'].next_by_code('account.move.fpjp') or '/'
        #         else:
        #             move.name = self.env['ir.sequence'].next_by_code('account.move.vendor') or '/'
        return result

    def write(self, values):
        """
        Overrides the write method to perform specific actions on account.move records.
        
        - Temporarily disables move validity checks by using a context switch.
        - Validates that in_invoice moves in draft state have at least one line.
        - Adjusts debit values based on account type for lines with a debit.
        - Updates credit values for lines with liability payable account type.
        - Prints detailed information of each line and the total debit and credit.

        :param values: Dictionary of field values to write on the records.
        :raises ValidationError: If 'in_invoice' in draft state has no lines and 'dynamic_unlink' is not in context.
        :return: Result of the super write method.
        """
        res = super(AccountMove, self.with_context(check_move_validity=False)).write(values)
        for record in self:
            if record.move_type == 'in_invoice' and record.validation_state == 'draft':
                if len(record.line_ids) < 1 and not self._context.get('dynamic_unlink'):
                    raise ValidationError('At least one line must be added to the invoice.')
                for line in record.line_ids.filtered_domain([('debit','>',0)]):
                    if line.account_id.account_type != 'asset_prepayments':
                        line.write({'debit': line.price_subtotal_bill})
                    else:
                        if record.tax_amount > 0 :
                            line.write({'debit': record.tax_amount})
                for payable in record.line_ids.filtered_domain([('credit','>',0)]):
                    if payable.account_id.account_type == 'liability_payable':
                        payable.write({'credit': record.total_amount})
                    elif payable.account_id.account_type == 'liability_current':
                        payable.write({'credit': abs(record.tax_amount_pph)})
        return res

    @api.onchange('partner_id')
    def onchange_partner_vendor(self):
        if self.partner_id:
            vendor_contact_id = self.env['res.partner'].search(
                [('is_company', '=', False), ('parent_id', '=', self.partner_id.id)], limit=1)
            self.vendor_contact_id = vendor_contact_id.id if vendor_contact_id else False

    @api.depends('move_type', 'date')
    def _compute_domain_partner(self):
        for rec in self:
            if rec.move_type == 'in_invoice':
                domain_partner = self.env['res.partner'].search(
                    [('company_id', 'in', (False, self.company_id.id)),
                     ('parent_id', '=', False), ('vendor_state', '=', 'spendauthorized')])
            elif rec.move_type == 'out_invoice':
                domain_partner = self.env['res.partner'].search(
                    [('company_id', 'in', (False, self.company_id.id)),
                     ('parent_id', '=', False), ('state', '=', 'active')])
            else:
                domain_partner = self.env['res.partner'].search([('company_id', 'in', (False, self.company_id.id))])
            
            rec.domain_partner_ids = domain_partner.ids

    @api.depends('move_type')
    def _compute_date(self):
        super()._compute_date()
        for move in self:
            if move.move_type == 'in_invoice' and move.invoice_date and move.invoice_date.day > 24:
                move.date = (move.invoice_date + relativedelta(months=1)).replace(day=1)
                # _affect_tax_report may trigger premature recompute of line_ids.date
                self.env.add_to_compute(move.line_ids._fields['date'], move.line_ids)
                # might be protected because `_get_accounting_date` requires the `name`
                self.env.add_to_compute(self._fields['name'], move)

    @api.depends('invoice_date', 'invoice_payment_term_id')
    def _compute_invoice_date_due(self):
        super()._compute_invoice_date_due()
        for move in self:
            if not move.invoice_payment_term_id:
                continue

            date_ref = move.invoice_date or fields.Date.context_today(move)
            move.invoice_date_due = max(move.invoice_payment_term_id.line_ids.mapped(lambda l: l._get_due_date(date_ref)))

    # @contextmanager
    # def _check_balanced(self, container):
    #     ''' Assert the move is fully balanced debit = credit.
    #     An error is raised if it's not the case.
    #     '''
    #     with self._disable_recursion(container, 'check_move_validity', default=True, target=False) as disabled:
    #         yield
    #         if disabled:
    #             return
    #     return

        # unbalanced_moves = self._get_unbalanced_moves(container)
        # if unbalanced_moves:
        #     error_msg = _("An error has occurred.")
        #     for move_id, sum_debit, sum_credit in unbalanced_moves:
        #         move = self.browse(move_id)
        #         error_msg += _(
        #             "\n\n"
        #             "The move (%(move)s) is not balanced.\n"
        #             "The total of debits equals %(debit_total)s and the total of credits equals %(credit_total)s.\n"
        #             "You might want to specify a default account on journal \"%(journal)s\" to automatically balance each move.",
        #             move=move.display_name,
        #             debit_total=format_amount(self.env, sum_debit, move.company_id.currency_id),
        #             credit_total=format_amount(self.env, sum_credit, move.company_id.currency_id),
        #             journal=move.journal_id.name)
        #     raise UserError(error_msg)