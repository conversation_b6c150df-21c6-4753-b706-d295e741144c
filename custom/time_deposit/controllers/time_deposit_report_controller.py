# -*- coding: utf-8 -*-

from odoo import http
from odoo.http import request, content_disposition
import io
import xlsxwriter
from datetime import datetime


class TimeDepositReportController(http.Controller):

    # @http.route('/time_deposit/debug_data', type='http', auth='user', csrf=False)
    # def debug_time_deposit_data(self, **kwargs):
    #     """Debug endpoint to check time deposit data"""
    #     business_unit_id = int(kwargs.get('business_unit_id', 1))
    #     date = kwargs.get('date', '2020-01-01')
    #     open_date = kwargs.get('open_date', '2030-12-31')

    #     # Build the same base domain as the report
    #     base_domain = [
    #         ('company_id', '=', business_unit_id),
    #         ('open_date', '>=', date),
    #         ('open_date', '<=', open_date),
    #         ('state', '!=', 'extend'),
    #     ]

    #     # Get all time deposits with base domain
    #     all_deposits = request.env['time.deposit'].sudo().search(base_domain)

    #     debug_info = {
    #         'base_domain': str(base_domain),
    #         'total_deposits': len(all_deposits),
    #         'swift_codes': [],
    #         'deposit_products': [],
    #         'sample_deposits': [],
    #         'test_filters': {}
    #     }

    #     swift_codes = set()
    #     products = set()

    #     for deposit in all_deposits[:10]:  # First 10 deposits
    #         bank_name = None
    #         swift_code = None
    #         if deposit.deposit_partner_bank_id:
    #             bank_name = deposit.deposit_partner_bank_id.name
    #             swift_code = deposit.deposit_partner_bank_id.swift_code

    #         sample_info = {
    #             'name': deposit.name,
    #             'state': deposit.state,
    #             'deposit_product': deposit.deposit_product,
    #             'deposit_partner_bank_id': bank_name,
    #             'swift_code': swift_code,
    #             'open_date': str(deposit.open_date) if deposit.open_date else None,
    #             'amount': deposit.deposit_original_amount,
    #         }
    #         debug_info['sample_deposits'].append(sample_info)

    #         if swift_code:
    #             swift_codes.add(swift_code)
    #         if deposit.deposit_product:
    #             products.add(deposit.deposit_product)

    #     debug_info['swift_codes'] = list(swift_codes)
    #     debug_info['deposit_products'] = list(products)

    #     # Test specific filters
    #     test_filters = [
    #         ('deposit_partner_bank_id.swift_code', '=', '********'),
    #         ('deposit_partner_bank_id.swift_code', '=', '********'),
    #         ('deposit_product', '=', 'syariah'),
    #         ('deposit_product', '=', 'konven'),
    #     ]

    #     for filter_condition in test_filters:
    #         test_domain = base_domain + [filter_condition]
    #         count = len(request.env['time.deposit'].sudo().search(test_domain))
    #         debug_info['test_filters'][str(filter_condition)] = count

    #     import json
    #     return request.make_response(
    #         json.dumps(debug_info, indent=2),
    #         headers=[('Content-Type', 'application/json')]
    #     )

    @http.route('/time_deposit/excel_report', type='http', auth='user', csrf=False)
    def generate_time_deposit_excel_report(self, **kwargs):
        """Generate Excel report for time deposits"""

        # Get parameters from request
        business_unit_id = int(kwargs.get('business_unit_id', 0))
        date = kwargs.get('date', '')
        open_date = kwargs.get('open_date', '')
        product = kwargs.get('product', 'all')
        deposit_type = kwargs.get('deposit_type', 'all')

        # Build base domain for filtering
        base_domain = [
            ('company_id', '=', business_unit_id),
            ('open_date', '>=', date),
            ('open_date', '<=', open_date),
            ('state', '!=', 'extend'),  # Exclude deposits with extend status
        ]

        # Check if both filters are 'all' - then create 4 separate tables
        if product == 'all' and deposit_type == 'all':
            return self._generate_multiple_tables_report(
                base_domain, business_unit_id, date, open_date
            )

        # Original single table logic for specific filters
        domain = base_domain.copy()

        # Add product filter with SWIFT code consideration
        if product == 'konven':
            # For 'konven' filter, only show BNI bank accounts (SWIFT: ********)
            domain.append(('deposit_partner_bank_id.swift_code', '=', '********'))
        elif product == 'merchant':
            # For 'merchant' filter, only show BRI bank accounts (SWIFT: ********)
            domain.append(('deposit_partner_bank_id.swift_code', '=', '********'))
        elif product == 'syariah':
            domain.append(('deposit_product', '=', 'syariah'))
        elif product != 'all':
            # For other specific product filters, use original logic
            domain.append(('deposit_product', '=', product))

        # Add deposit type filter
        if deposit_type != 'all':
            domain.append(('deposit_type', '=', deposit_type))

        # Get time deposit records
        time_deposits = request.env['time.deposit'].sudo().search(domain, order='open_date asc')

        # Check if no data found
        if not time_deposits:
            # Return a simple Excel with "No data found" message
            output = io.BytesIO()
            workbook = xlsxwriter.Workbook(output, {'in_memory': True})
            sheet = workbook.add_worksheet('Time Deposit Report')

            cell_format = workbook.add_format({
                'bold': True,
                'align': 'center',
                'valign': 'vcenter'
            })

            sheet.write(0, 0, 'No data found for the selected criteria', cell_format)
            workbook.close()
            output.seek(0)

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'time_deposit_report_{timestamp}.xlsx'

            response = request.make_response(
                output.read(),
                headers=[
                    ('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'),
                    ('Content-Disposition', content_disposition(filename))
                ]
            )
            return response

        # Get business unit name
        business_unit = request.env['res.company'].sudo().browse(business_unit_id)
        business_unit_name = business_unit.name if business_unit else ''
        
        # Get product and deposit type labels
        product_dict = dict(request.env['time.deposit']._fields['deposit_product'].selection)
        deposit_type_dict = dict(request.env['time.deposit']._fields['deposit_type'].selection)

        # Simple labels for specific product filters
        if product == 'konven':
            product_label = 'Konven'
        elif product == 'merchant':
            product_label = 'Merchant'
        elif product == 'syariah':
            product_label = 'Syariah'
        elif product == 'all':
            product_label = 'All'
        else:
            product_label = product_dict.get(product, product)

        deposit_type_label = deposit_type_dict.get(deposit_type, 'All') if deposit_type != 'all' else 'All'
        
        # Prepare Excel file
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})
        sheet = workbook.add_worksheet('Time Deposit Report')
        
        # Define formats
        title_format = workbook.add_format({
            'bold': True,
            'font_size': 16,
            'align': 'center',
            'valign': 'vcenter'
        })
        
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#D3D3D3',
            'border': 1,
            'align': 'center',
            'valign': 'vcenter',
            'text_wrap': True
        })
        
        cell_format = workbook.add_format({
            'border': 1,
            'align': 'left',
            'valign': 'vcenter'
        })
        
        number_format = workbook.add_format({
            'border': 1,
            'align': 'right',
            'valign': 'vcenter',
            'num_format': '#,##0.00'
        })
        
        # Set column widths
        sheet.set_column('A:A', 5)   # No
        sheet.set_column('B:B', 15)  # Deposit Number
        sheet.set_column('C:C', 12)  # Open Date
        sheet.set_column('D:D', 12)  # Mature Date
        sheet.set_column('E:E', 12)  # Deposit In Days
        sheet.set_column('F:F', 15)  # Bank
        sheet.set_column('G:G', 15)  # Amount
        sheet.set_column('H:H', 12)  # Interest Rate
        sheet.set_column('I:I', 15)  # Beneficiary Bank
        sheet.set_column('J:J', 15)  # Bank Account
        sheet.set_column('K:K', 10)  # Type
        sheet.set_column('L:L', 15)  # No Bilyet Deposito
        sheet.set_column('M:M', 12)  # Classification
        sheet.set_column('N:N', 15)  # Remarks
        sheet.set_column('O:O', 12)  # Breakable %
        sheet.set_column('P:P', 15)  # Deposit Status
        
        # Write title
        sheet.merge_range('A1:P1', 'List Time Deposit Report', title_format)

        # Write filter information
        row = 2
        sheet.write(row, 0, f'Business Unit: {business_unit_name}', cell_format)
        row += 1
        sheet.write(row, 0, f'Date: {date}', cell_format)
        row += 1
        sheet.write(row, 0, f'Open Date: {open_date}', cell_format)
        row += 1
        sheet.write(row, 0, f'Product: {product_label}', cell_format)
        row += 1
        sheet.write(row, 0, f'Deposit Type: {deposit_type_label}', cell_format)

        # Skip a row
        row += 2

        # Write headers
        headers = [
            'No', 'Deposit Number', 'Open Date', 'Mature Date', 'Deposit In Days',
            'Bank', 'Amount', 'Interest Rate', 'Beneficiary Bank', 'Bank Account',
            'Type', 'No Bilyet Deposito', 'Classification', 'Remarks', 'Breakable %',
            'Deposit Status'
        ]

        for col, header in enumerate(headers):
            sheet.write(row, col, header, header_format)

        # Write data
        row += 1
        for idx, deposit in enumerate(time_deposits, 1):
            sheet.write(row, 0, idx, cell_format)
            sheet.write(row, 1, deposit.name or '', cell_format)

            # Open Date
            if deposit.open_date:
                sheet.write(row, 2, deposit.open_date.strftime('%d-%m-%Y'), cell_format)
            else:
                sheet.write(row, 2, '', cell_format)

            # Mature Date
            if deposit.maturity_date:
                sheet.write(row, 3, deposit.maturity_date.strftime('%d-%m-%Y'), cell_format)
            else:
                sheet.write(row, 3, '', cell_format)

            sheet.write(row, 4, deposit.deposit_in_days_placement or 0, cell_format)
            sheet.write(row, 5, self._format_bank_name(deposit.bank) or '', cell_format)
            sheet.write(row, 6, deposit.deposit_original_amount or 0, number_format)
            sheet.write(row, 7, deposit.interest_rate or 0, number_format)

            # Beneficiary Bank
            beneficiary_bank = ''
            if deposit.beneficiary_bank_id and deposit.beneficiary_bank_id.bank_id:
                beneficiary_bank = self._format_bank_name(deposit.beneficiary_bank_id.bank_id.name)
            sheet.write(row, 8, beneficiary_bank, cell_format)
            sheet.write(row, 9, deposit.bank_account or '', cell_format)

            # Type (deposit_type)
            sheet.write(row, 10, deposit_type_dict.get(deposit.deposit_type, ''), cell_format)

            sheet.write(row, 11, '', cell_format)  # No Bilyet Deposito - empty for now
            sheet.write(row, 12, deposit.deposit_clasification or '', cell_format)
            sheet.write(row, 13, deposit.deposit_note or '', cell_format)
            sheet.write(row, 14, '', cell_format)  # Breakable % - empty for now

            # Deposit Status (state)
            state_dict = dict(deposit._fields['state'].selection)
            sheet.write(row, 15, state_dict.get(deposit.state, ''), cell_format)

            row += 1

        # Close workbook and prepare response
        workbook.close()
        output.seek(0)
        
        # Generate filename with timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'time_deposit_report_{timestamp}.xlsx'
        
        # Create response
        response = request.make_response(
            output.read(),
            headers=[
                ('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'),
                ('Content-Disposition', content_disposition(filename))
            ]
        )
        
        return response

    def _generate_multiple_tables_report(self, base_domain, business_unit_id, date, open_date):
        """Generate Excel report with 4 separate tables when both filters are 'all'"""

        # Get business unit name
        business_unit = request.env['res.company'].sudo().browse(business_unit_id)
        business_unit_name = business_unit.name if business_unit else ''

        # Prepare Excel file
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})

        # Define formats
        title_format = workbook.add_format({
            'bold': True,
            'font_size': 16,
            'align': 'center',
            'valign': 'vcenter'
        })

        subtitle_format = workbook.add_format({
            'bold': True,
            'font_size': 14,
            'align': 'left',
            'valign': 'vcenter',
            'bg_color': '#E6E6FA'
        })

        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#D3D3D3',
            'border': 1,
            'align': 'center',
            'valign': 'vcenter',
            'text_wrap': True
        })

        cell_format = workbook.add_format({
            'border': 1,
            'align': 'left',
            'valign': 'vcenter'
        })

        number_format = workbook.add_format({
            'border': 1,
            'align': 'right',
            'valign': 'vcenter',
            'num_format': '#,##0.00'
        })

        # Create worksheet
        sheet = workbook.add_worksheet('Time Deposit Report')

        # Set column widths
        sheet.set_column('A:A', 5)   # No
        sheet.set_column('B:B', 15)  # Deposit Number
        sheet.set_column('C:C', 12)  # Open Date
        sheet.set_column('D:D', 12)  # Mature Date
        sheet.set_column('E:E', 12)  # Deposit In Days
        sheet.set_column('F:F', 15)  # Bank
        sheet.set_column('G:G', 15)  # Amount
        sheet.set_column('H:H', 12)  # Interest Rate
        sheet.set_column('I:I', 15)  # Beneficiary Bank
        sheet.set_column('J:J', 15)  # Bank Account
        sheet.set_column('K:K', 10)  # Type
        sheet.set_column('L:L', 15)  # No Bilyet Deposito
        sheet.set_column('M:M', 12)  # Classification
        sheet.set_column('N:N', 15)  # Remarks
        sheet.set_column('O:O', 12)  # Breakable %
        sheet.set_column('P:P', 15)  # Deposit Status

        # Write main title
        sheet.merge_range('A1:P1', 'List Time Deposit Report', title_format)

        # Write filter information
        row = 2
        sheet.write(row, 0, f'Business Unit: {business_unit_name}', cell_format)
        row += 1
        sheet.write(row, 0, f'Date: {date}', cell_format)
        row += 1
        sheet.write(row, 0, f'Open Date: {open_date}', cell_format)
        row += 1
        sheet.write(row, 0, 'Product: All', cell_format)
        row += 1
        sheet.write(row, 0, 'Deposit Type: All', cell_format)

        # Skip a row
        row += 2

        # Debug: Check what swift codes exist in the database
        import logging
        _logger = logging.getLogger(__name__)

        # Get all time deposits with base domain
        all_deposits = request.env['time.deposit'].sudo().search(base_domain)
        _logger.info(f"Total deposits found with base domain: {len(all_deposits)}")

        # Check swift codes and create dynamic table configs
        swift_codes = set()
        products = set()
        for deposit in all_deposits:
            if deposit.deposit_partner_bank_id and deposit.deposit_partner_bank_id.swift_code:
                swift_codes.add(deposit.deposit_partner_bank_id.swift_code)
            if deposit.deposit_product:
                products.add(deposit.deposit_product)

        _logger.info(f"Swift codes found in deposits: {list(swift_codes)}")
        _logger.info(f"Deposit products found: {list(products)}")

        # Update table configs based on actual data
        updated_table_configs = []

        # If no specific categorization is possible, show all data in one table
        if not swift_codes and not products:
            _logger.warning("No swift codes or products found, showing all deposits in single table")
            updated_table_configs.append({
                'title': 'All Time Deposits',
                'domain_filter': ('id', '>', 0)  # Show all records
            })
        else:
            # Deposito Bank Konvensional IDR - check if swift code exists
            if '********' in swift_codes:
                updated_table_configs.append({
                    'title': 'Deposito Bank Konvensional IDR',
                    'domain_filter': ('deposit_partner_bank_id.swift_code', '=', '********')
                })
            elif 'konven' in products:
                # Fallback: try to find konven product
                _logger.warning("Swift code ******** not found, using konven product filter")
                updated_table_configs.append({
                    'title': 'Deposito Bank Konvensional IDR',
                    'domain_filter': ('deposit_product', '=', 'konven')
                })

            # Deposito Bank Konvensional Merchant - check if swift code exists
            if '********' in swift_codes:
                updated_table_configs.append({
                    'title': 'Deposito Bank Konvensional Merchant',
                    'domain_filter': ('deposit_partner_bank_id.swift_code', '=', '********')
                })

            # Deposito Bank Syariah
            if 'syariah' in products:
                updated_table_configs.append({
                    'title': 'Deposito Bank Syariah',
                    'domain_filter': ('deposit_product', '=', 'syariah')
                })

            # If no specific categories found, show all deposits
            if not updated_table_configs:
                _logger.warning("No matching categories found, showing all deposits")
                updated_table_configs.append({
                    'title': 'All Time Deposits',
                    'domain_filter': ('id', '>', 0)  # Show all records
                })

            # Deposito USD - keep empty for now
            updated_table_configs.append({
                'title': 'Deposito USD',
                'domain_filter': None
            })

        # Generate each table
        for config in updated_table_configs:
            row = self._write_table_section(
                sheet, config, base_domain, row,
                subtitle_format, header_format, cell_format, number_format
            )
            row += 2  # Add space between tables

        # Add total row using raw SQL query
        row = self._write_total_section(sheet, workbook, row, business_unit_id, date, open_date)

        # Close workbook and prepare response
        workbook.close()
        output.seek(0)

        # Generate filename with timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'time_deposit_report_all_{timestamp}.xlsx'

        # Create response
        response = request.make_response(
            output.read(),
            headers=[
                ('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'),
                ('Content-Disposition', content_disposition(filename))
            ]
        )

        return response

    def _write_table_section(self, sheet, config, base_domain, start_row,
                           subtitle_format, header_format, cell_format, number_format):
        """Write a table section for specific category"""

        # Build domain for this table
        domain = base_domain.copy()
        if config['domain_filter']:
            domain.append(config['domain_filter'])

        # Debug logging
        import logging
        _logger = logging.getLogger(__name__)
        _logger.info(f"Table: {config['title']}")
        _logger.info(f"Base domain: {base_domain}")
        _logger.info(f"Domain filter: {config['domain_filter']}")
        _logger.info(f"Final domain: {domain}")

        # Get data for this table
        if config['domain_filter']:
            time_deposits = request.env['time.deposit'].sudo().search(domain, order='open_date asc')
            _logger.info(f"Found {len(time_deposits)} deposits for {config['title']}")

            # Additional debug: check if there are any deposits without domain filter
            all_deposits = request.env['time.deposit'].sudo().search(
                base_domain, order='open_date asc'
            )
            _logger.info(f"Total deposits with base domain only: {len(all_deposits)}")

            # Check if the specific field exists and has values
            if all_deposits:
                sample_deposit = all_deposits[0]
                _logger.info(f"Sample deposit: {sample_deposit.name}")
                _logger.info(
                    f"Sample deposit partner bank: {sample_deposit.deposit_partner_bank_id}"
                )
                if sample_deposit.deposit_partner_bank_id:
                    swift_code = sample_deposit.deposit_partner_bank_id.swift_code
                    bank_name = sample_deposit.deposit_partner_bank_id.name
                    _logger.info(f"Sample swift code: {swift_code}")
                    _logger.info(f"Sample bank name: {bank_name}")
        else:
            time_deposits = request.env['time.deposit'].sudo().browse([])  # Empty for USD

        # Write table title
        sheet.merge_range(f'A{start_row + 1}:P{start_row + 1}', config['title'], subtitle_format)
        row = start_row + 2

        # Write headers
        headers = [
            'No', 'Deposit Number', 'Open Date', 'Mature Date', 'Deposit In Days',
            'Bank', 'Amount', 'Interest Rate', 'Beneficiary Bank', 'Bank Account',
            'Type', 'No Bilyet Deposito', 'Classification', 'Remarks', 'Breakable %',
            'Deposit Status'
        ]

        for col, header in enumerate(headers):
            sheet.write(row, col, header, header_format)

        # Write data
        row += 1
        if time_deposits:
            deposit_type_dict = dict(request.env['time.deposit']._fields['deposit_type'].selection)

            for idx, deposit in enumerate(time_deposits, 1):
                sheet.write(row, 0, idx, cell_format)
                sheet.write(row, 1, deposit.name or '', cell_format)

                # Open Date
                if deposit.open_date:
                    sheet.write(row, 2, deposit.open_date.strftime('%d-%m-%Y'), cell_format)
                else:
                    sheet.write(row, 2, '', cell_format)

                # Mature Date
                if deposit.maturity_date:
                    sheet.write(row, 3, deposit.maturity_date.strftime('%d-%m-%Y'), cell_format)
                else:
                    sheet.write(row, 3, '', cell_format)

                sheet.write(row, 4, deposit.deposit_in_days_placement or 0, cell_format)
                sheet.write(row, 5, self._format_bank_name(deposit.bank) or '', cell_format)
                sheet.write(row, 6, deposit.deposit_original_amount or 0, number_format)
                sheet.write(row, 7, deposit.interest_rate or 0, number_format)

                # Beneficiary Bank
                beneficiary_bank = ''
                if deposit.beneficiary_bank_id and deposit.beneficiary_bank_id.bank_id:
                    beneficiary_bank = self._format_bank_name(deposit.beneficiary_bank_id.bank_id.name)
                sheet.write(row, 8, beneficiary_bank, cell_format)
                sheet.write(row, 9, deposit.bank_account or '', cell_format)

                # Type (deposit_type)
                sheet.write(row, 10, deposit_type_dict.get(deposit.deposit_type, ''), cell_format)

                sheet.write(row, 11, '', cell_format)  # No Bilyet Deposito - empty for now
                sheet.write(row, 12, deposit.deposit_clasification or '', cell_format)
                sheet.write(row, 13, deposit.deposit_note or '', cell_format)
                sheet.write(row, 14, '', cell_format)  # Breakable % - empty for now

                # Deposit Status (state)
                state_dict = dict(deposit._fields['state'].selection)
                sheet.write(row, 15, state_dict.get(deposit.state, ''), cell_format)

                row += 1
        else:
            # Write "No data" message
            sheet.merge_range(f'A{row + 1}:P{row + 1}', 'No data available', cell_format)
            row += 1

        return row

    def _write_total_section(self, sheet, workbook, start_row, business_unit_id, date, open_date):
        """Write total section using raw SQL query"""

        # Build SQL query to calculate total amount
        sql_query = """
            SELECT COALESCE(SUM(deposit_original_amount), 0) as total_amount
            FROM time_deposit
            WHERE company_id = %s
            AND open_date >= %s
            AND open_date <= %s
            AND state != 'extend'
        """

        # Execute query
        try:
            request.env.cr.execute(sql_query, (business_unit_id, date, open_date))
            result = request.env.cr.fetchone()
            total_amount = result[0] if result else 0

            # Debug: Log the result
            import logging
            _logger = logging.getLogger(__name__)
            _logger.info(f"Total amount calculated: {total_amount} for company {business_unit_id}, date range {date} to {open_date}")

        except Exception as e:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error(f"Error calculating total: {e}")
            total_amount = 0

        # Create special format for total row
        total_label_format = workbook.add_format({
            'bold': True,
            'border': 1,
            'align': 'left',
            'valign': 'vcenter',
            'bg_color': '#E6E6FA'
        })

        total_colon_format = workbook.add_format({
            'bold': True,
            'border': 1,
            'align': 'center',
            'valign': 'vcenter',
            'bg_color': '#E6E6FA'
        })

        total_amount_format = workbook.add_format({
            'bold': True,
            'border': 1,
            'align': 'right',
            'valign': 'vcenter',
            'num_format': '#,##0.00',
            'bg_color': '#E6E6FA'
        })

        # Write total row
        row = start_row + 2
        sheet.write(row, 0, 'Total Time Deposit', total_label_format)
        sheet.write(row, 1, ':', total_colon_format)
        sheet.write(row, 2, total_amount, total_amount_format)

        return row + 1

    def _format_bank_name(self, bank_name):
        """Format bank name by removing 'Bank' prefix and replacing full names with abbreviations"""
        if not bank_name:
            return ''
        # Replace specific bank names with their abbreviations
        bank_mappings = {
            'Bank Rakyat Indonesia': 'BRI',
        }
        # Try exact match first
        if bank_name in bank_mappings:
            return bank_mappings[bank_name]
        # Remove 'Bank' prefix if present
        if bank_name.startswith('Bank '):
            return bank_name[5:].strip()
        return bank_name
